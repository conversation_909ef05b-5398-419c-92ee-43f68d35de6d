(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))n(o);new MutationObserver(o=>{for(const r of o)if(r.type==="childList")for(const l of r.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&n(l)}).observe(document,{childList:!0,subtree:!0});function s(o){const r={};return o.integrity&&(r.integrity=o.integrity),o.referrerPolicy&&(r.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?r.credentials="include":o.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function n(o){if(o.ep)return;o.ep=!0;const r=s(o);fetch(o.href,r)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Bn(t){const e=Object.create(null);for(const s of t.split(","))e[s]=1;return s=>s in e}const ht={},Ve=[],ee=()=>{},Wr=()=>!1,Us=t=>t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&(t.charCodeAt(2)>122||t.charCodeAt(2)<97),Nn=t=>t.startsWith("onUpdate:"),wt=Object.assign,Hn=(t,e)=>{const s=t.indexOf(e);s>-1&&t.splice(s,1)},Vr=Object.prototype.hasOwnProperty,at=(t,e)=>Vr.call(t,e),U=Array.isArray,Ke=t=>qs(t)==="[object Map]",di=t=>qs(t)==="[object Set]",J=t=>typeof t=="function",bt=t=>typeof t=="string",$e=t=>typeof t=="symbol",yt=t=>t!==null&&typeof t=="object",fi=t=>(yt(t)||J(t))&&J(t.then)&&J(t.catch),pi=Object.prototype.toString,qs=t=>pi.call(t),Kr=t=>qs(t).slice(8,-1),mi=t=>qs(t)==="[object Object]",zn=t=>bt(t)&&t!=="NaN"&&t[0]!=="-"&&""+parseInt(t,10)===t,ss=Bn(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Gs=t=>{const e=Object.create(null);return s=>e[s]||(e[s]=t(s))},Ur=/-(\w)/g,Wt=Gs(t=>t.replace(Ur,(e,s)=>s?s.toUpperCase():"")),qr=/\B([A-Z])/g,Oe=Gs(t=>t.replace(qr,"-$1").toLowerCase()),Ys=Gs(t=>t.charAt(0).toUpperCase()+t.slice(1)),on=Gs(t=>t?`on${Ys(t)}`:""),ke=(t,e)=>!Object.is(t,e),rn=(t,...e)=>{for(let s=0;s<t.length;s++)t[s](...e)},wn=(t,e,s,n=!1)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,writable:n,value:s})},Gr=t=>{const e=parseFloat(t);return isNaN(e)?t:e},Yr=t=>{const e=bt(t)?Number(t):NaN;return isNaN(e)?t:e};let co;const Js=()=>co||(co=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ps(t){if(U(t)){const e={};for(let s=0;s<t.length;s++){const n=t[s],o=bt(n)?Zr(n):ps(n);if(o)for(const r in o)e[r]=o[r]}return e}else if(bt(t)||yt(t))return t}const Jr=/;(?![^(]*\))/g,Qr=/:([^]+)/,Xr=/\/\*[^]*?\*\//g;function Zr(t){const e={};return t.replace(Xr,"").split(Jr).forEach(s=>{if(s){const n=s.split(Qr);n.length>1&&(e[n[0].trim()]=n[1].trim())}}),e}function Ft(t){let e="";if(bt(t))e=t;else if(U(t))for(let s=0;s<t.length;s++){const n=Ft(t[s]);n&&(e+=n+" ")}else if(yt(t))for(const s in t)t[s]&&(e+=s+" ");return e.trim()}const tl="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",el=Bn(tl);function hi(t){return!!t||t===""}const gi=t=>!!(t&&t.__v_isRef===!0),b=t=>bt(t)?t:t==null?"":U(t)||yt(t)&&(t.toString===pi||!J(t.toString))?gi(t)?b(t.value):JSON.stringify(t,xi,2):String(t),xi=(t,e)=>gi(e)?xi(t,e.value):Ke(e)?{[`Map(${e.size})`]:[...e.entries()].reduce((s,[n,o],r)=>(s[ln(n,r)+" =>"]=o,s),{})}:di(e)?{[`Set(${e.size})`]:[...e.values()].map(s=>ln(s))}:$e(e)?ln(e):yt(e)&&!U(e)&&!mi(e)?String(e):e,ln=(t,e="")=>{var s;return $e(t)?`Symbol(${(s=t.description)!=null?s:e})`:t};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let $t;class yi{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=$t,!e&&$t&&(this.index=($t.scopes||($t.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let e,s;if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].pause();for(e=0,s=this.effects.length;e<s;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let e,s;if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].resume();for(e=0,s=this.effects.length;e<s;e++)this.effects[e].resume()}}run(e){if(this._active){const s=$t;try{return $t=this,e()}finally{$t=s}}}on(){++this._on===1&&(this.prevScope=$t,$t=this)}off(){this._on>0&&--this._on===0&&($t=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function vi(t){return new yi(t)}function bi(){return $t}function sl(t,e=!1){$t&&$t.cleanups.push(t)}let mt;const an=new WeakSet;class _i{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,$t&&$t.active&&$t.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,an.has(this)&&(an.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ki(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,uo(this),Si(this);const e=mt,s=Vt;mt=this,Vt=!0;try{return this.fn()}finally{Ci(this),mt=e,Vt=s,this.flags&=-3}}stop(){if(this.flags&1){for(let e=this.deps;e;e=e.nextDep)Kn(e);this.deps=this.depsTail=void 0,uo(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?an.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){kn(this)&&this.run()}get dirty(){return kn(this)}}let wi=0,ns,os;function ki(t,e=!1){if(t.flags|=8,e){t.next=os,os=t;return}t.next=ns,ns=t}function Wn(){wi++}function Vn(){if(--wi>0)return;if(os){let e=os;for(os=void 0;e;){const s=e.next;e.next=void 0,e.flags&=-9,e=s}}let t;for(;ns;){let e=ns;for(ns=void 0;e;){const s=e.next;if(e.next=void 0,e.flags&=-9,e.flags&1)try{e.trigger()}catch(n){t||(t=n)}e=s}}if(t)throw t}function Si(t){for(let e=t.deps;e;e=e.nextDep)e.version=-1,e.prevActiveLink=e.dep.activeLink,e.dep.activeLink=e}function Ci(t){let e,s=t.depsTail,n=s;for(;n;){const o=n.prevDep;n.version===-1?(n===s&&(s=o),Kn(n),nl(n)):e=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=o}t.deps=e,t.depsTail=s}function kn(t){for(let e=t.deps;e;e=e.nextDep)if(e.dep.version!==e.version||e.dep.computed&&($i(e.dep.computed)||e.dep.version!==e.version))return!0;return!!t._dirty}function $i(t){if(t.flags&4&&!(t.flags&16)||(t.flags&=-17,t.globalVersion===ms)||(t.globalVersion=ms,!t.isSSR&&t.flags&128&&(!t.deps&&!t._dirty||!kn(t))))return;t.flags|=2;const e=t.dep,s=mt,n=Vt;mt=t,Vt=!0;try{Si(t);const o=t.fn(t._value);(e.version===0||ke(o,t._value))&&(t.flags|=128,t._value=o,e.version++)}catch(o){throw e.version++,o}finally{mt=s,Vt=n,Ci(t),t.flags&=-3}}function Kn(t,e=!1){const{dep:s,prevSub:n,nextSub:o}=t;if(n&&(n.nextSub=o,t.prevSub=void 0),o&&(o.prevSub=n,t.nextSub=void 0),s.subs===t&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let r=s.computed.deps;r;r=r.nextDep)Kn(r,!0)}!e&&!--s.sc&&s.map&&s.map.delete(s.key)}function nl(t){const{prevDep:e,nextDep:s}=t;e&&(e.nextDep=s,t.prevDep=void 0),s&&(s.prevDep=e,t.nextDep=void 0)}let Vt=!0;const Pi=[];function ue(){Pi.push(Vt),Vt=!1}function de(){const t=Pi.pop();Vt=t===void 0?!0:t}function uo(t){const{cleanup:e}=t;if(t.cleanup=void 0,e){const s=mt;mt=void 0;try{e()}finally{mt=s}}}let ms=0;class ol{constructor(e,s){this.sub=e,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Un{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!mt||!Vt||mt===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==mt)s=this.activeLink=new ol(mt,this),mt.deps?(s.prevDep=mt.depsTail,mt.depsTail.nextDep=s,mt.depsTail=s):mt.deps=mt.depsTail=s,Ti(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=mt.depsTail,s.nextDep=void 0,mt.depsTail.nextDep=s,mt.depsTail=s,mt.deps===s&&(mt.deps=n)}return s}trigger(e){this.version++,ms++,this.notify(e)}notify(e){Wn();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Vn()}}}function Ti(t){if(t.dep.sc++,t.sub.flags&4){const e=t.dep.computed;if(e&&!t.dep.subs){e.flags|=20;for(let n=e.deps;n;n=n.nextDep)Ti(n)}const s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t}}const Fs=new WeakMap,Re=Symbol(""),Sn=Symbol(""),hs=Symbol("");function Pt(t,e,s){if(Vt&&mt){let n=Fs.get(t);n||Fs.set(t,n=new Map);let o=n.get(s);o||(n.set(s,o=new Un),o.map=n,o.key=s),o.track()}}function ae(t,e,s,n,o,r){const l=Fs.get(t);if(!l){ms++;return}const a=c=>{c&&c.trigger()};if(Wn(),e==="clear")l.forEach(a);else{const c=U(t),p=c&&zn(s);if(c&&s==="length"){const f=Number(n);l.forEach((u,d)=>{(d==="length"||d===hs||!$e(d)&&d>=f)&&a(u)})}else switch((s!==void 0||l.has(void 0))&&a(l.get(s)),p&&a(l.get(hs)),e){case"add":c?p&&a(l.get("length")):(a(l.get(Re)),Ke(t)&&a(l.get(Sn)));break;case"delete":c||(a(l.get(Re)),Ke(t)&&a(l.get(Sn)));break;case"set":Ke(t)&&a(l.get(Re));break}}Vn()}function il(t,e){const s=Fs.get(t);return s&&s.get(e)}function Fe(t){const e=nt(t);return e===t?e:(Pt(e,"iterate",hs),zt(t)?e:e.map(St))}function Qs(t){return Pt(t=nt(t),"iterate",hs),t}const rl={__proto__:null,[Symbol.iterator](){return cn(this,Symbol.iterator,St)},concat(...t){return Fe(this).concat(...t.map(e=>U(e)?Fe(e):e))},entries(){return cn(this,"entries",t=>(t[1]=St(t[1]),t))},every(t,e){return ie(this,"every",t,e,void 0,arguments)},filter(t,e){return ie(this,"filter",t,e,s=>s.map(St),arguments)},find(t,e){return ie(this,"find",t,e,St,arguments)},findIndex(t,e){return ie(this,"findIndex",t,e,void 0,arguments)},findLast(t,e){return ie(this,"findLast",t,e,St,arguments)},findLastIndex(t,e){return ie(this,"findLastIndex",t,e,void 0,arguments)},forEach(t,e){return ie(this,"forEach",t,e,void 0,arguments)},includes(...t){return un(this,"includes",t)},indexOf(...t){return un(this,"indexOf",t)},join(t){return Fe(this).join(t)},lastIndexOf(...t){return un(this,"lastIndexOf",t)},map(t,e){return ie(this,"map",t,e,void 0,arguments)},pop(){return Qe(this,"pop")},push(...t){return Qe(this,"push",t)},reduce(t,...e){return fo(this,"reduce",t,e)},reduceRight(t,...e){return fo(this,"reduceRight",t,e)},shift(){return Qe(this,"shift")},some(t,e){return ie(this,"some",t,e,void 0,arguments)},splice(...t){return Qe(this,"splice",t)},toReversed(){return Fe(this).toReversed()},toSorted(t){return Fe(this).toSorted(t)},toSpliced(...t){return Fe(this).toSpliced(...t)},unshift(...t){return Qe(this,"unshift",t)},values(){return cn(this,"values",St)}};function cn(t,e,s){const n=Qs(t),o=n[e]();return n!==t&&!zt(t)&&(o._next=o.next,o.next=()=>{const r=o._next();return r.value&&(r.value=s(r.value)),r}),o}const ll=Array.prototype;function ie(t,e,s,n,o,r){const l=Qs(t),a=l!==t&&!zt(t),c=l[e];if(c!==ll[e]){const u=c.apply(t,r);return a?St(u):u}let p=s;l!==t&&(a?p=function(u,d){return s.call(this,St(u),d,t)}:s.length>2&&(p=function(u,d){return s.call(this,u,d,t)}));const f=c.call(l,p,n);return a&&o?o(f):f}function fo(t,e,s,n){const o=Qs(t);let r=s;return o!==t&&(zt(t)?s.length>3&&(r=function(l,a,c){return s.call(this,l,a,c,t)}):r=function(l,a,c){return s.call(this,l,St(a),c,t)}),o[e](r,...n)}function un(t,e,s){const n=nt(t);Pt(n,"iterate",hs);const o=n[e](...s);return(o===-1||o===!1)&&Yn(s[0])?(s[0]=nt(s[0]),n[e](...s)):o}function Qe(t,e,s=[]){ue(),Wn();const n=nt(t)[e].apply(t,s);return Vn(),de(),n}const al=Bn("__proto__,__v_isRef,__isVue"),Ei=new Set(Object.getOwnPropertyNames(Symbol).filter(t=>t!=="arguments"&&t!=="caller").map(t=>Symbol[t]).filter($e));function cl(t){$e(t)||(t=String(t));const e=nt(this);return Pt(e,"has",t),e.hasOwnProperty(t)}class Mi{constructor(e=!1,s=!1){this._isReadonly=e,this._isShallow=s}get(e,s,n){if(s==="__v_skip")return e.__v_skip;const o=this._isReadonly,r=this._isShallow;if(s==="__v_isReactive")return!o;if(s==="__v_isReadonly")return o;if(s==="__v_isShallow")return r;if(s==="__v_raw")return n===(o?r?vl:Oi:r?Di:Ai).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const l=U(e);if(!o){let c;if(l&&(c=rl[s]))return c;if(s==="hasOwnProperty")return cl}const a=Reflect.get(e,s,vt(e)?e:n);return($e(s)?Ei.has(s):al(s))||(o||Pt(e,"get",s),r)?a:vt(a)?l&&zn(s)?a:a.value:yt(a)?o?Li(a):ws(a):a}}class Ri extends Mi{constructor(e=!1){super(!1,e)}set(e,s,n,o){let r=e[s];if(!this._isShallow){const c=Ce(r);if(!zt(n)&&!Ce(n)&&(r=nt(r),n=nt(n)),!U(e)&&vt(r)&&!vt(n))return c?!1:(r.value=n,!0)}const l=U(e)&&zn(s)?Number(s)<e.length:at(e,s),a=Reflect.set(e,s,n,vt(e)?e:o);return e===nt(o)&&(l?ke(n,r)&&ae(e,"set",s,n):ae(e,"add",s,n)),a}deleteProperty(e,s){const n=at(e,s);e[s];const o=Reflect.deleteProperty(e,s);return o&&n&&ae(e,"delete",s,void 0),o}has(e,s){const n=Reflect.has(e,s);return(!$e(s)||!Ei.has(s))&&Pt(e,"has",s),n}ownKeys(e){return Pt(e,"iterate",U(e)?"length":Re),Reflect.ownKeys(e)}}class ul extends Mi{constructor(e=!1){super(!0,e)}set(e,s){return!0}deleteProperty(e,s){return!0}}const dl=new Ri,fl=new ul,pl=new Ri(!0);const Cn=t=>t,Ts=t=>Reflect.getPrototypeOf(t);function ml(t,e,s){return function(...n){const o=this.__v_raw,r=nt(o),l=Ke(r),a=t==="entries"||t===Symbol.iterator&&l,c=t==="keys"&&l,p=o[t](...n),f=s?Cn:e?Is:St;return!e&&Pt(r,"iterate",c?Sn:Re),{next(){const{value:u,done:d}=p.next();return d?{value:u,done:d}:{value:a?[f(u[0]),f(u[1])]:f(u),done:d}},[Symbol.iterator](){return this}}}}function Es(t){return function(...e){return t==="delete"?!1:t==="clear"?void 0:this}}function hl(t,e){const s={get(o){const r=this.__v_raw,l=nt(r),a=nt(o);t||(ke(o,a)&&Pt(l,"get",o),Pt(l,"get",a));const{has:c}=Ts(l),p=e?Cn:t?Is:St;if(c.call(l,o))return p(r.get(o));if(c.call(l,a))return p(r.get(a));r!==l&&r.get(o)},get size(){const o=this.__v_raw;return!t&&Pt(nt(o),"iterate",Re),Reflect.get(o,"size",o)},has(o){const r=this.__v_raw,l=nt(r),a=nt(o);return t||(ke(o,a)&&Pt(l,"has",o),Pt(l,"has",a)),o===a?r.has(o):r.has(o)||r.has(a)},forEach(o,r){const l=this,a=l.__v_raw,c=nt(a),p=e?Cn:t?Is:St;return!t&&Pt(c,"iterate",Re),a.forEach((f,u)=>o.call(r,p(f),p(u),l))}};return wt(s,t?{add:Es("add"),set:Es("set"),delete:Es("delete"),clear:Es("clear")}:{add(o){!e&&!zt(o)&&!Ce(o)&&(o=nt(o));const r=nt(this);return Ts(r).has.call(r,o)||(r.add(o),ae(r,"add",o,o)),this},set(o,r){!e&&!zt(r)&&!Ce(r)&&(r=nt(r));const l=nt(this),{has:a,get:c}=Ts(l);let p=a.call(l,o);p||(o=nt(o),p=a.call(l,o));const f=c.call(l,o);return l.set(o,r),p?ke(r,f)&&ae(l,"set",o,r):ae(l,"add",o,r),this},delete(o){const r=nt(this),{has:l,get:a}=Ts(r);let c=l.call(r,o);c||(o=nt(o),c=l.call(r,o)),a&&a.call(r,o);const p=r.delete(o);return c&&ae(r,"delete",o,void 0),p},clear(){const o=nt(this),r=o.size!==0,l=o.clear();return r&&ae(o,"clear",void 0,void 0),l}}),["keys","values","entries",Symbol.iterator].forEach(o=>{s[o]=ml(o,t,e)}),s}function qn(t,e){const s=hl(t,e);return(n,o,r)=>o==="__v_isReactive"?!t:o==="__v_isReadonly"?t:o==="__v_raw"?n:Reflect.get(at(s,o)&&o in n?s:n,o,r)}const gl={get:qn(!1,!1)},xl={get:qn(!1,!0)},yl={get:qn(!0,!1)};const Ai=new WeakMap,Di=new WeakMap,Oi=new WeakMap,vl=new WeakMap;function bl(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function _l(t){return t.__v_skip||!Object.isExtensible(t)?0:bl(Kr(t))}function ws(t){return Ce(t)?t:Gn(t,!1,dl,gl,Ai)}function ji(t){return Gn(t,!1,pl,xl,Di)}function Li(t){return Gn(t,!0,fl,yl,Oi)}function Gn(t,e,s,n,o){if(!yt(t)||t.__v_raw&&!(e&&t.__v_isReactive))return t;const r=_l(t);if(r===0)return t;const l=o.get(t);if(l)return l;const a=new Proxy(t,r===2?n:s);return o.set(t,a),a}function Se(t){return Ce(t)?Se(t.__v_raw):!!(t&&t.__v_isReactive)}function Ce(t){return!!(t&&t.__v_isReadonly)}function zt(t){return!!(t&&t.__v_isShallow)}function Yn(t){return t?!!t.__v_raw:!1}function nt(t){const e=t&&t.__v_raw;return e?nt(e):t}function Jn(t){return!at(t,"__v_skip")&&Object.isExtensible(t)&&wn(t,"__v_skip",!0),t}const St=t=>yt(t)?ws(t):t,Is=t=>yt(t)?Li(t):t;function vt(t){return t?t.__v_isRef===!0:!1}function Ct(t){return Fi(t,!1)}function wl(t){return Fi(t,!0)}function Fi(t,e){return vt(t)?t:new kl(t,e)}class kl{constructor(e,s){this.dep=new Un,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?e:nt(e),this._value=s?e:St(e),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(e){const s=this._rawValue,n=this.__v_isShallow||zt(e)||Ce(e);e=n?e:nt(e),ke(e,s)&&(this._rawValue=e,this._value=n?e:St(e),this.dep.trigger())}}function x(t){return vt(t)?t.value:t}const Sl={get:(t,e,s)=>e==="__v_raw"?t:x(Reflect.get(t,e,s)),set:(t,e,s,n)=>{const o=t[e];return vt(o)&&!vt(s)?(o.value=s,!0):Reflect.set(t,e,s,n)}};function Ii(t){return Se(t)?t:new Proxy(t,Sl)}function Cl(t){const e=U(t)?new Array(t.length):{};for(const s in t)e[s]=Pl(t,s);return e}class $l{constructor(e,s,n){this._object=e,this._key=s,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const e=this._object[this._key];return this._value=e===void 0?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return il(nt(this._object),this._key)}}function Pl(t,e,s){const n=t[e];return vt(n)?n:new $l(t,e,s)}class Tl{constructor(e,s,n){this.fn=e,this.setter=s,this._value=void 0,this.dep=new Un(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=ms-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&mt!==this)return ki(this,!0),!0}get value(){const e=this.dep.track();return $i(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}function El(t,e,s=!1){let n,o;return J(t)?n=t:(n=t.get,o=t.set),new Tl(n,o,s)}const Ms={},Bs=new WeakMap;let Me;function Ml(t,e=!1,s=Me){if(s){let n=Bs.get(s);n||Bs.set(s,n=[]),n.push(t)}}function Rl(t,e,s=ht){const{immediate:n,deep:o,once:r,scheduler:l,augmentJob:a,call:c}=s,p=M=>o?M:zt(M)||o===!1||o===0?we(M,1):we(M);let f,u,d,g,k=!1,T=!1;if(vt(t)?(u=()=>t.value,k=zt(t)):Se(t)?(u=()=>p(t),k=!0):U(t)?(T=!0,k=t.some(M=>Se(M)||zt(M)),u=()=>t.map(M=>{if(vt(M))return M.value;if(Se(M))return p(M);if(J(M))return c?c(M,2):M()})):J(t)?e?u=c?()=>c(t,2):t:u=()=>{if(d){ue();try{d()}finally{de()}}const M=Me;Me=f;try{return c?c(t,3,[g]):t(g)}finally{Me=M}}:u=ee,e&&o){const M=u,V=o===!0?1/0:o;u=()=>we(M(),V)}const N=bi(),B=()=>{f.stop(),N&&N.active&&Hn(N.effects,f)};if(r&&e){const M=e;e=(...V)=>{M(...V),B()}}let C=T?new Array(t.length).fill(Ms):Ms;const P=M=>{if(!(!(f.flags&1)||!f.dirty&&!M))if(e){const V=f.run();if(o||k||(T?V.some((Z,et)=>ke(Z,C[et])):ke(V,C))){d&&d();const Z=Me;Me=f;try{const et=[V,C===Ms?void 0:T&&C[0]===Ms?[]:C,g];C=V,c?c(e,3,et):e(...et)}finally{Me=Z}}}else f.run()};return a&&a(P),f=new _i(u),f.scheduler=l?()=>l(P,!1):P,g=M=>Ml(M,!1,f),d=f.onStop=()=>{const M=Bs.get(f);if(M){if(c)c(M,4);else for(const V of M)V();Bs.delete(f)}},e?n?P(!0):C=f.run():l?l(P.bind(null,!0),!0):f.run(),B.pause=f.pause.bind(f),B.resume=f.resume.bind(f),B.stop=B,B}function we(t,e=1/0,s){if(e<=0||!yt(t)||t.__v_skip||(s=s||new Set,s.has(t)))return t;if(s.add(t),e--,vt(t))we(t.value,e,s);else if(U(t))for(let n=0;n<t.length;n++)we(t[n],e,s);else if(di(t)||Ke(t))t.forEach(n=>{we(n,e,s)});else if(mi(t)){for(const n in t)we(t[n],e,s);for(const n of Object.getOwnPropertySymbols(t))Object.prototype.propertyIsEnumerable.call(t,n)&&we(t[n],e,s)}return t}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function ks(t,e,s,n){try{return n?t(...n):t()}catch(o){Xs(o,e,s)}}function Kt(t,e,s,n){if(J(t)){const o=ks(t,e,s,n);return o&&fi(o)&&o.catch(r=>{Xs(r,e,s)}),o}if(U(t)){const o=[];for(let r=0;r<t.length;r++)o.push(Kt(t[r],e,s,n));return o}}function Xs(t,e,s,n=!0){const o=e?e.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:l}=e&&e.appContext.config||ht;if(e){let a=e.parent;const c=e.proxy,p=`https://vuejs.org/error-reference/#runtime-${s}`;for(;a;){const f=a.ec;if(f){for(let u=0;u<f.length;u++)if(f[u](t,c,p)===!1)return}a=a.parent}if(r){ue(),ks(r,null,10,[t,c,p]),de();return}}Al(t,s,o,n,l)}function Al(t,e,s,n=!0,o=!1){if(o)throw t;console.error(t)}const Et=[];let Zt=-1;const Ue=[];let ve=null,Ne=0;const Bi=Promise.resolve();let Ns=null;function Qn(t){const e=Ns||Bi;return t?e.then(this?t.bind(this):t):e}function Dl(t){let e=Zt+1,s=Et.length;for(;e<s;){const n=e+s>>>1,o=Et[n],r=gs(o);r<t||r===t&&o.flags&2?e=n+1:s=n}return e}function Xn(t){if(!(t.flags&1)){const e=gs(t),s=Et[Et.length-1];!s||!(t.flags&2)&&e>=gs(s)?Et.push(t):Et.splice(Dl(e),0,t),t.flags|=1,Ni()}}function Ni(){Ns||(Ns=Bi.then(zi))}function Ol(t){U(t)?Ue.push(...t):ve&&t.id===-1?ve.splice(Ne+1,0,t):t.flags&1||(Ue.push(t),t.flags|=1),Ni()}function po(t,e,s=Zt+1){for(;s<Et.length;s++){const n=Et[s];if(n&&n.flags&2){if(t&&n.id!==t.uid)continue;Et.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function Hi(t){if(Ue.length){const e=[...new Set(Ue)].sort((s,n)=>gs(s)-gs(n));if(Ue.length=0,ve){ve.push(...e);return}for(ve=e,Ne=0;Ne<ve.length;Ne++){const s=ve[Ne];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}ve=null,Ne=0}}const gs=t=>t.id==null?t.flags&2?-1:1/0:t.id;function zi(t){try{for(Zt=0;Zt<Et.length;Zt++){const e=Et[Zt];e&&!(e.flags&8)&&(e.flags&4&&(e.flags&=-2),ks(e,e.i,e.i?15:14),e.flags&4||(e.flags&=-2))}}finally{for(;Zt<Et.length;Zt++){const e=Et[Zt];e&&(e.flags&=-2)}Zt=-1,Et.length=0,Hi(),Ns=null,(Et.length||Ue.length)&&zi()}}let It=null,Wi=null;function Hs(t){const e=It;return It=t,Wi=t&&t.type.__scopeId||null,e}function We(t,e=It,s){if(!e||t._n)return t;const n=(...o)=>{n._d&&ko(-1);const r=Hs(e);let l;try{l=t(...o)}finally{Hs(r),n._d&&ko(1)}return l};return n._n=!0,n._c=!0,n._d=!0,n}function Pe(t,e,s,n){const o=t.dirs,r=e&&e.dirs;for(let l=0;l<o.length;l++){const a=o[l];r&&(a.oldValue=r[l].value);let c=a.dir[n];c&&(ue(),Kt(c,s,8,[t.el,a,t,e]),de())}}const jl=Symbol("_vte"),Ll=t=>t.__isTeleport,Ie=Symbol("_leaveCb"),Rs=Symbol("_enterCb");function Fl(){const t={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Gi(()=>{t.isMounted=!0}),Ji(()=>{t.isUnmounting=!0}),t}const Ht=[Function,Array],Il={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ht,onEnter:Ht,onAfterEnter:Ht,onEnterCancelled:Ht,onBeforeLeave:Ht,onLeave:Ht,onAfterLeave:Ht,onLeaveCancelled:Ht,onBeforeAppear:Ht,onAppear:Ht,onAfterAppear:Ht,onAppearCancelled:Ht};function Bl(t,e){const{leavingVNodes:s}=t;let n=s.get(e.type);return n||(n=Object.create(null),s.set(e.type,n)),n}function $n(t,e,s,n,o){const{appear:r,mode:l,persisted:a=!1,onBeforeEnter:c,onEnter:p,onAfterEnter:f,onEnterCancelled:u,onBeforeLeave:d,onLeave:g,onAfterLeave:k,onLeaveCancelled:T,onBeforeAppear:N,onAppear:B,onAfterAppear:C,onAppearCancelled:P}=e,M=String(t.key),V=Bl(s,t),Z=(D,q)=>{D&&Kt(D,n,9,q)},et=(D,q)=>{const tt=q[1];Z(D,q),U(D)?D.every(A=>A.length<=1)&&tt():D.length<=1&&tt()},G={mode:l,persisted:a,beforeEnter(D){let q=c;if(!s.isMounted)if(r)q=N||c;else return;D[Ie]&&D[Ie](!0);const tt=V[M];tt&&He(t,tt)&&tt.el[Ie]&&tt.el[Ie](),Z(q,[D])},enter(D){let q=p,tt=f,A=u;if(!s.isMounted)if(r)q=B||p,tt=C||f,A=P||u;else return;let X=!1;const ft=D[Rs]=_t=>{X||(X=!0,_t?Z(A,[D]):Z(tt,[D]),G.delayedLeave&&G.delayedLeave(),D[Rs]=void 0)};q?et(q,[D,ft]):ft()},leave(D,q){const tt=String(t.key);if(D[Rs]&&D[Rs](!0),s.isUnmounting)return q();Z(d,[D]);let A=!1;const X=D[Ie]=ft=>{A||(A=!0,q(),ft?Z(T,[D]):Z(k,[D]),D[Ie]=void 0,V[tt]===t&&delete V[tt])};V[tt]=t,g?et(g,[D,X]):X()},clone(D){return $n(D,e,s,n)}};return G}function xs(t,e){t.shapeFlag&6&&t.component?(t.transition=e,xs(t.component.subTree,e)):t.shapeFlag&128?(t.ssContent.transition=e.clone(t.ssContent),t.ssFallback.transition=e.clone(t.ssFallback)):t.transition=e}function Vi(t,e=!1,s){let n=[],o=0;for(let r=0;r<t.length;r++){let l=t[r];const a=s==null?l.key:String(s)+String(l.key!=null?l.key:r);l.type===gt?(l.patchFlag&128&&o++,n=n.concat(Vi(l.children,e,a))):(e||l.type!==fe)&&n.push(a!=null?De(l,{key:a}):l)}if(o>1)for(let r=0;r<n.length;r++)n[r].patchFlag=-2;return n}/*! #__NO_SIDE_EFFECTS__ */function Ot(t,e){return J(t)?wt({name:t.name},e,{setup:t}):t}function Ki(t){t.ids=[t.ids[0]+t.ids[2]+++"-",0,0]}function is(t,e,s,n,o=!1){if(U(t)){t.forEach((k,T)=>is(k,e&&(U(e)?e[T]:e),s,n,o));return}if(rs(n)&&!o){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&is(t,e,s,n.component.subTree);return}const r=n.shapeFlag&4?so(n.component):n.el,l=o?null:r,{i:a,r:c}=t,p=e&&e.r,f=a.refs===ht?a.refs={}:a.refs,u=a.setupState,d=nt(u),g=u===ht?()=>!1:k=>at(d,k);if(p!=null&&p!==c&&(bt(p)?(f[p]=null,g(p)&&(u[p]=null)):vt(p)&&(p.value=null)),J(c))ks(c,a,12,[l,f]);else{const k=bt(c),T=vt(c);if(k||T){const N=()=>{if(t.f){const B=k?g(c)?u[c]:f[c]:c.value;o?U(B)&&Hn(B,r):U(B)?B.includes(r)||B.push(r):k?(f[c]=[r],g(c)&&(u[c]=f[c])):(c.value=[r],t.k&&(f[t.k]=c.value))}else k?(f[c]=l,g(c)&&(u[c]=l)):T&&(c.value=l,t.k&&(f[t.k]=l))};l?(N.id=-1,Lt(N,s)):N()}}}Js().requestIdleCallback;Js().cancelIdleCallback;const rs=t=>!!t.type.__asyncLoader,Ui=t=>t.type.__isKeepAlive;function Nl(t,e){qi(t,"a",e)}function Hl(t,e){qi(t,"da",e)}function qi(t,e,s=kt){const n=t.__wdc||(t.__wdc=()=>{let o=s;for(;o;){if(o.isDeactivated)return;o=o.parent}return t()});if(Zs(e,n,s),s){let o=s.parent;for(;o&&o.parent;)Ui(o.parent.vnode)&&zl(n,e,s,o),o=o.parent}}function zl(t,e,s,n){const o=Zs(e,t,n,!0);Qi(()=>{Hn(n[e],o)},s)}function Zs(t,e,s=kt,n=!1){if(s){const o=s[t]||(s[t]=[]),r=e.__weh||(e.__weh=(...l)=>{ue();const a=Ss(s),c=Kt(e,s,t,l);return a(),de(),c});return n?o.unshift(r):o.push(r),r}}const pe=t=>(e,s=kt)=>{(!vs||t==="sp")&&Zs(t,(...n)=>e(...n),s)},Wl=pe("bm"),Gi=pe("m"),Vl=pe("bu"),Yi=pe("u"),Ji=pe("bum"),Qi=pe("um"),Kl=pe("sp"),Ul=pe("rtg"),ql=pe("rtc");function Gl(t,e=kt){Zs("ec",t,e)}const Yl="components";function Jl(t,e){return Xl(Yl,t,!0,e)||t}const Ql=Symbol.for("v-ndc");function Xl(t,e,s=!0,n=!1){const o=It||kt;if(o){const r=o.type;{const a=Ha(r,!1);if(a&&(a===e||a===Wt(e)||a===Ys(Wt(e))))return r}const l=mo(o[t]||r[t],e)||mo(o.appContext[t],e);return!l&&n?r:l}}function mo(t,e){return t&&(t[e]||t[Wt(e)]||t[Ys(Wt(e))])}function Dt(t,e,s,n){let o;const r=s,l=U(t);if(l||bt(t)){const a=l&&Se(t);let c=!1,p=!1;a&&(c=!zt(t),p=Ce(t),t=Qs(t)),o=new Array(t.length);for(let f=0,u=t.length;f<u;f++)o[f]=e(c?p?Is(St(t[f])):St(t[f]):t[f],f,void 0,r)}else if(typeof t=="number"){o=new Array(t);for(let a=0;a<t;a++)o[a]=e(a+1,a,void 0,r)}else if(yt(t))if(t[Symbol.iterator])o=Array.from(t,(a,c)=>e(a,c,void 0,r));else{const a=Object.keys(t);o=new Array(a.length);for(let c=0,p=a.length;c<p;c++){const f=a[c];o[c]=e(t[f],f,c,r)}}else o=[];return o}const Pn=t=>t?xr(t)?so(t):Pn(t.parent):null,ls=wt(Object.create(null),{$:t=>t,$el:t=>t.vnode.el,$data:t=>t.data,$props:t=>t.props,$attrs:t=>t.attrs,$slots:t=>t.slots,$refs:t=>t.refs,$parent:t=>Pn(t.parent),$root:t=>Pn(t.root),$host:t=>t.ce,$emit:t=>t.emit,$options:t=>Zi(t),$forceUpdate:t=>t.f||(t.f=()=>{Xn(t.update)}),$nextTick:t=>t.n||(t.n=Qn.bind(t.proxy)),$watch:t=>ba.bind(t)}),dn=(t,e)=>t!==ht&&!t.__isScriptSetup&&at(t,e),Zl={get({_:t},e){if(e==="__v_skip")return!0;const{ctx:s,setupState:n,data:o,props:r,accessCache:l,type:a,appContext:c}=t;let p;if(e[0]!=="$"){const g=l[e];if(g!==void 0)switch(g){case 1:return n[e];case 2:return o[e];case 4:return s[e];case 3:return r[e]}else{if(dn(n,e))return l[e]=1,n[e];if(o!==ht&&at(o,e))return l[e]=2,o[e];if((p=t.propsOptions[0])&&at(p,e))return l[e]=3,r[e];if(s!==ht&&at(s,e))return l[e]=4,s[e];Tn&&(l[e]=0)}}const f=ls[e];let u,d;if(f)return e==="$attrs"&&Pt(t.attrs,"get",""),f(t);if((u=a.__cssModules)&&(u=u[e]))return u;if(s!==ht&&at(s,e))return l[e]=4,s[e];if(d=c.config.globalProperties,at(d,e))return d[e]},set({_:t},e,s){const{data:n,setupState:o,ctx:r}=t;return dn(o,e)?(o[e]=s,!0):n!==ht&&at(n,e)?(n[e]=s,!0):at(t.props,e)||e[0]==="$"&&e.slice(1)in t?!1:(r[e]=s,!0)},has({_:{data:t,setupState:e,accessCache:s,ctx:n,appContext:o,propsOptions:r}},l){let a;return!!s[l]||t!==ht&&at(t,l)||dn(e,l)||(a=r[0])&&at(a,l)||at(n,l)||at(ls,l)||at(o.config.globalProperties,l)},defineProperty(t,e,s){return s.get!=null?t._.accessCache[e]=0:at(s,"value")&&this.set(t,e,s.value,null),Reflect.defineProperty(t,e,s)}};function ho(t){return U(t)?t.reduce((e,s)=>(e[s]=null,e),{}):t}let Tn=!0;function ta(t){const e=Zi(t),s=t.proxy,n=t.ctx;Tn=!1,e.beforeCreate&&go(e.beforeCreate,t,"bc");const{data:o,computed:r,methods:l,watch:a,provide:c,inject:p,created:f,beforeMount:u,mounted:d,beforeUpdate:g,updated:k,activated:T,deactivated:N,beforeDestroy:B,beforeUnmount:C,destroyed:P,unmounted:M,render:V,renderTracked:Z,renderTriggered:et,errorCaptured:G,serverPrefetch:D,expose:q,inheritAttrs:tt,components:A,directives:X,filters:ft}=e;if(p&&ea(p,n,null),l)for(const Q in l){const rt=l[Q];J(rt)&&(n[Q]=rt.bind(s))}if(o){const Q=o.call(s,s);yt(Q)&&(t.data=ws(Q))}if(Tn=!0,r)for(const Q in r){const rt=r[Q],oe=J(rt)?rt.bind(s,s):J(rt.get)?rt.get.bind(s,s):ee,me=!J(rt)&&J(rt.set)?rt.set.bind(s):ee,qt=xt({get:oe,set:me});Object.defineProperty(n,Q,{enumerable:!0,configurable:!0,get:()=>qt.value,set:Mt=>qt.value=Mt})}if(a)for(const Q in a)Xi(a[Q],n,s,Q);if(c){const Q=J(c)?c.call(s):c;Reflect.ownKeys(Q).forEach(rt=>{Ds(rt,Q[rt])})}f&&go(f,t,"c");function ct(Q,rt){U(rt)?rt.forEach(oe=>Q(oe.bind(s))):rt&&Q(rt.bind(s))}if(ct(Wl,u),ct(Gi,d),ct(Vl,g),ct(Yi,k),ct(Nl,T),ct(Hl,N),ct(Gl,G),ct(ql,Z),ct(Ul,et),ct(Ji,C),ct(Qi,M),ct(Kl,D),U(q))if(q.length){const Q=t.exposed||(t.exposed={});q.forEach(rt=>{Object.defineProperty(Q,rt,{get:()=>s[rt],set:oe=>s[rt]=oe})})}else t.exposed||(t.exposed={});V&&t.render===ee&&(t.render=V),tt!=null&&(t.inheritAttrs=tt),A&&(t.components=A),X&&(t.directives=X),D&&Ki(t)}function ea(t,e,s=ee){U(t)&&(t=En(t));for(const n in t){const o=t[n];let r;yt(o)?"default"in o?r=se(o.from||n,o.default,!0):r=se(o.from||n):r=se(o),vt(r)?Object.defineProperty(e,n,{enumerable:!0,configurable:!0,get:()=>r.value,set:l=>r.value=l}):e[n]=r}}function go(t,e,s){Kt(U(t)?t.map(n=>n.bind(e.proxy)):t.bind(e.proxy),e,s)}function Xi(t,e,s,n){let o=n.includes(".")?fr(s,n):()=>s[n];if(bt(t)){const r=e[t];J(r)&&as(o,r)}else if(J(t))as(o,t.bind(s));else if(yt(t))if(U(t))t.forEach(r=>Xi(r,e,s,n));else{const r=J(t.handler)?t.handler.bind(s):e[t.handler];J(r)&&as(o,r,t)}}function Zi(t){const e=t.type,{mixins:s,extends:n}=e,{mixins:o,optionsCache:r,config:{optionMergeStrategies:l}}=t.appContext,a=r.get(e);let c;return a?c=a:!o.length&&!s&&!n?c=e:(c={},o.length&&o.forEach(p=>zs(c,p,l,!0)),zs(c,e,l)),yt(e)&&r.set(e,c),c}function zs(t,e,s,n=!1){const{mixins:o,extends:r}=e;r&&zs(t,r,s,!0),o&&o.forEach(l=>zs(t,l,s,!0));for(const l in e)if(!(n&&l==="expose")){const a=sa[l]||s&&s[l];t[l]=a?a(t[l],e[l]):e[l]}return t}const sa={data:xo,props:yo,emits:yo,methods:ts,computed:ts,beforeCreate:Tt,created:Tt,beforeMount:Tt,mounted:Tt,beforeUpdate:Tt,updated:Tt,beforeDestroy:Tt,beforeUnmount:Tt,destroyed:Tt,unmounted:Tt,activated:Tt,deactivated:Tt,errorCaptured:Tt,serverPrefetch:Tt,components:ts,directives:ts,watch:oa,provide:xo,inject:na};function xo(t,e){return e?t?function(){return wt(J(t)?t.call(this,this):t,J(e)?e.call(this,this):e)}:e:t}function na(t,e){return ts(En(t),En(e))}function En(t){if(U(t)){const e={};for(let s=0;s<t.length;s++)e[t[s]]=t[s];return e}return t}function Tt(t,e){return t?[...new Set([].concat(t,e))]:e}function ts(t,e){return t?wt(Object.create(null),t,e):e}function yo(t,e){return t?U(t)&&U(e)?[...new Set([...t,...e])]:wt(Object.create(null),ho(t),ho(e??{})):e}function oa(t,e){if(!t)return e;if(!e)return t;const s=wt(Object.create(null),t);for(const n in e)s[n]=Tt(t[n],e[n]);return s}function tr(){return{app:null,config:{isNativeTag:Wr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ia=0;function ra(t,e){return function(n,o=null){J(n)||(n=wt({},n)),o!=null&&!yt(o)&&(o=null);const r=tr(),l=new WeakSet,a=[];let c=!1;const p=r.app={_uid:ia++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:Wa,get config(){return r.config},set config(f){},use(f,...u){return l.has(f)||(f&&J(f.install)?(l.add(f),f.install(p,...u)):J(f)&&(l.add(f),f(p,...u))),p},mixin(f){return r.mixins.includes(f)||r.mixins.push(f),p},component(f,u){return u?(r.components[f]=u,p):r.components[f]},directive(f,u){return u?(r.directives[f]=u,p):r.directives[f]},mount(f,u,d){if(!c){const g=p._ceVNode||it(n,o);return g.appContext=r,d===!0?d="svg":d===!1&&(d=void 0),t(g,f,d),c=!0,p._container=f,f.__vue_app__=p,so(g.component)}},onUnmount(f){a.push(f)},unmount(){c&&(Kt(a,p._instance,16),t(null,p._container),delete p._container.__vue_app__)},provide(f,u){return r.provides[f]=u,p},runWithContext(f){const u=Ae;Ae=p;try{return f()}finally{Ae=u}}};return p}}let Ae=null;function Ds(t,e){if(kt){let s=kt.provides;const n=kt.parent&&kt.parent.provides;n===s&&(s=kt.provides=Object.create(n)),s[t]=e}}function se(t,e,s=!1){const n=kt||It;if(n||Ae){let o=Ae?Ae._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(o&&t in o)return o[t];if(arguments.length>1)return s&&J(e)?e.call(n&&n.proxy):e}}function la(){return!!(kt||It||Ae)}const er={},sr=()=>Object.create(er),nr=t=>Object.getPrototypeOf(t)===er;function aa(t,e,s,n=!1){const o={},r=sr();t.propsDefaults=Object.create(null),or(t,e,o,r);for(const l in t.propsOptions[0])l in o||(o[l]=void 0);s?t.props=n?o:ji(o):t.type.props?t.props=o:t.props=r,t.attrs=r}function ca(t,e,s,n){const{props:o,attrs:r,vnode:{patchFlag:l}}=t,a=nt(o),[c]=t.propsOptions;let p=!1;if((n||l>0)&&!(l&16)){if(l&8){const f=t.vnode.dynamicProps;for(let u=0;u<f.length;u++){let d=f[u];if(tn(t.emitsOptions,d))continue;const g=e[d];if(c)if(at(r,d))g!==r[d]&&(r[d]=g,p=!0);else{const k=Wt(d);o[k]=Mn(c,a,k,g,t,!1)}else g!==r[d]&&(r[d]=g,p=!0)}}}else{or(t,e,o,r)&&(p=!0);let f;for(const u in a)(!e||!at(e,u)&&((f=Oe(u))===u||!at(e,f)))&&(c?s&&(s[u]!==void 0||s[f]!==void 0)&&(o[u]=Mn(c,a,u,void 0,t,!0)):delete o[u]);if(r!==a)for(const u in r)(!e||!at(e,u))&&(delete r[u],p=!0)}p&&ae(t.attrs,"set","")}function or(t,e,s,n){const[o,r]=t.propsOptions;let l=!1,a;if(e)for(let c in e){if(ss(c))continue;const p=e[c];let f;o&&at(o,f=Wt(c))?!r||!r.includes(f)?s[f]=p:(a||(a={}))[f]=p:tn(t.emitsOptions,c)||(!(c in n)||p!==n[c])&&(n[c]=p,l=!0)}if(r){const c=nt(s),p=a||ht;for(let f=0;f<r.length;f++){const u=r[f];s[u]=Mn(o,c,u,p[u],t,!at(p,u))}}return l}function Mn(t,e,s,n,o,r){const l=t[s];if(l!=null){const a=at(l,"default");if(a&&n===void 0){const c=l.default;if(l.type!==Function&&!l.skipFactory&&J(c)){const{propsDefaults:p}=o;if(s in p)n=p[s];else{const f=Ss(o);n=p[s]=c.call(null,e),f()}}else n=c;o.ce&&o.ce._setProp(s,n)}l[0]&&(r&&!a?n=!1:l[1]&&(n===""||n===Oe(s))&&(n=!0))}return n}const ua=new WeakMap;function ir(t,e,s=!1){const n=s?ua:e.propsCache,o=n.get(t);if(o)return o;const r=t.props,l={},a=[];let c=!1;if(!J(t)){const f=u=>{c=!0;const[d,g]=ir(u,e,!0);wt(l,d),g&&a.push(...g)};!s&&e.mixins.length&&e.mixins.forEach(f),t.extends&&f(t.extends),t.mixins&&t.mixins.forEach(f)}if(!r&&!c)return yt(t)&&n.set(t,Ve),Ve;if(U(r))for(let f=0;f<r.length;f++){const u=Wt(r[f]);vo(u)&&(l[u]=ht)}else if(r)for(const f in r){const u=Wt(f);if(vo(u)){const d=r[f],g=l[u]=U(d)||J(d)?{type:d}:wt({},d),k=g.type;let T=!1,N=!0;if(U(k))for(let B=0;B<k.length;++B){const C=k[B],P=J(C)&&C.name;if(P==="Boolean"){T=!0;break}else P==="String"&&(N=!1)}else T=J(k)&&k.name==="Boolean";g[0]=T,g[1]=N,(T||at(g,"default"))&&a.push(u)}}const p=[l,a];return yt(t)&&n.set(t,p),p}function vo(t){return t[0]!=="$"&&!ss(t)}const Zn=t=>t[0]==="_"||t==="$stable",to=t=>U(t)?t.map(te):[te(t)],da=(t,e,s)=>{if(e._n)return e;const n=We((...o)=>to(e(...o)),s);return n._c=!1,n},rr=(t,e,s)=>{const n=t._ctx;for(const o in t){if(Zn(o))continue;const r=t[o];if(J(r))e[o]=da(o,r,n);else if(r!=null){const l=to(r);e[o]=()=>l}}},lr=(t,e)=>{const s=to(e);t.slots.default=()=>s},ar=(t,e,s)=>{for(const n in e)(s||!Zn(n))&&(t[n]=e[n])},fa=(t,e,s)=>{const n=t.slots=sr();if(t.vnode.shapeFlag&32){const o=e.__;o&&wn(n,"__",o,!0);const r=e._;r?(ar(n,e,s),s&&wn(n,"_",r,!0)):rr(e,n)}else e&&lr(t,e)},pa=(t,e,s)=>{const{vnode:n,slots:o}=t;let r=!0,l=ht;if(n.shapeFlag&32){const a=e._;a?s&&a===1?r=!1:ar(o,e,s):(r=!e.$stable,rr(e,o)),l=e}else e&&(lr(t,e),l={default:1});if(r)for(const a in o)!Zn(a)&&l[a]==null&&delete o[a]},Lt=Pa;function ma(t){return ha(t)}function ha(t,e){const s=Js();s.__VUE__=!0;const{insert:n,remove:o,patchProp:r,createElement:l,createText:a,createComment:c,setText:p,setElementText:f,parentNode:u,nextSibling:d,setScopeId:g=ee,insertStaticContent:k}=t,T=(m,h,y,v=null,S=null,w=null,O=void 0,R=null,E=!!h.dynamicChildren)=>{if(m===h)return;m&&!He(m,h)&&(v=_(m),Mt(m,S,w,!0),m=null),h.patchFlag===-2&&(E=!1,h.dynamicChildren=null);const{type:$,ref:K,shapeFlag:L}=h;switch($){case en:N(m,h,y,v);break;case fe:B(m,h,y,v);break;case Os:m==null&&C(h,y,v,O);break;case gt:A(m,h,y,v,S,w,O,R,E);break;default:L&1?V(m,h,y,v,S,w,O,R,E):L&6?X(m,h,y,v,S,w,O,R,E):(L&64||L&128)&&$.process(m,h,y,v,S,w,O,R,E,z)}K!=null&&S?is(K,m&&m.ref,w,h||m,!h):K==null&&m&&m.ref!=null&&is(m.ref,null,w,m,!0)},N=(m,h,y,v)=>{if(m==null)n(h.el=a(h.children),y,v);else{const S=h.el=m.el;h.children!==m.children&&p(S,h.children)}},B=(m,h,y,v)=>{m==null?n(h.el=c(h.children||""),y,v):h.el=m.el},C=(m,h,y,v)=>{[m.el,m.anchor]=k(m.children,h,y,v,m.el,m.anchor)},P=({el:m,anchor:h},y,v)=>{let S;for(;m&&m!==h;)S=d(m),n(m,y,v),m=S;n(h,y,v)},M=({el:m,anchor:h})=>{let y;for(;m&&m!==h;)y=d(m),o(m),m=y;o(h)},V=(m,h,y,v,S,w,O,R,E)=>{h.type==="svg"?O="svg":h.type==="math"&&(O="mathml"),m==null?Z(h,y,v,S,w,O,R,E):D(m,h,S,w,O,R,E)},Z=(m,h,y,v,S,w,O,R)=>{let E,$;const{props:K,shapeFlag:L,transition:W,dirs:Y}=m;if(E=m.el=l(m.type,w,K&&K.is,K),L&8?f(E,m.children):L&16&&G(m.children,E,null,v,S,fn(m,w),O,R),Y&&Pe(m,null,v,"created"),et(E,m,m.scopeId,O,v),K){for(const pt in K)pt!=="value"&&!ss(pt)&&r(E,pt,null,K[pt],w,v);"value"in K&&r(E,"value",null,K.value,w),($=K.onVnodeBeforeMount)&&Qt($,v,m)}Y&&Pe(m,null,v,"beforeMount");const st=ga(S,W);st&&W.beforeEnter(E),n(E,h,y),(($=K&&K.onVnodeMounted)||st||Y)&&Lt(()=>{$&&Qt($,v,m),st&&W.enter(E),Y&&Pe(m,null,v,"mounted")},S)},et=(m,h,y,v,S)=>{if(y&&g(m,y),v)for(let w=0;w<v.length;w++)g(m,v[w]);if(S){let w=S.subTree;if(h===w||mr(w.type)&&(w.ssContent===h||w.ssFallback===h)){const O=S.vnode;et(m,O,O.scopeId,O.slotScopeIds,S.parent)}}},G=(m,h,y,v,S,w,O,R,E=0)=>{for(let $=E;$<m.length;$++){const K=m[$]=R?be(m[$]):te(m[$]);T(null,K,h,y,v,S,w,O,R)}},D=(m,h,y,v,S,w,O)=>{const R=h.el=m.el;let{patchFlag:E,dynamicChildren:$,dirs:K}=h;E|=m.patchFlag&16;const L=m.props||ht,W=h.props||ht;let Y;if(y&&Te(y,!1),(Y=W.onVnodeBeforeUpdate)&&Qt(Y,y,h,m),K&&Pe(h,m,y,"beforeUpdate"),y&&Te(y,!0),(L.innerHTML&&W.innerHTML==null||L.textContent&&W.textContent==null)&&f(R,""),$?q(m.dynamicChildren,$,R,y,v,fn(h,S),w):O||rt(m,h,R,null,y,v,fn(h,S),w,!1),E>0){if(E&16)tt(R,L,W,y,S);else if(E&2&&L.class!==W.class&&r(R,"class",null,W.class,S),E&4&&r(R,"style",L.style,W.style,S),E&8){const st=h.dynamicProps;for(let pt=0;pt<st.length;pt++){const ut=st[pt],Rt=L[ut],At=W[ut];(At!==Rt||ut==="value")&&r(R,ut,Rt,At,S,y)}}E&1&&m.children!==h.children&&f(R,h.children)}else!O&&$==null&&tt(R,L,W,y,S);((Y=W.onVnodeUpdated)||K)&&Lt(()=>{Y&&Qt(Y,y,h,m),K&&Pe(h,m,y,"updated")},v)},q=(m,h,y,v,S,w,O)=>{for(let R=0;R<h.length;R++){const E=m[R],$=h[R],K=E.el&&(E.type===gt||!He(E,$)||E.shapeFlag&198)?u(E.el):y;T(E,$,K,null,v,S,w,O,!0)}},tt=(m,h,y,v,S)=>{if(h!==y){if(h!==ht)for(const w in h)!ss(w)&&!(w in y)&&r(m,w,h[w],null,S,v);for(const w in y){if(ss(w))continue;const O=y[w],R=h[w];O!==R&&w!=="value"&&r(m,w,R,O,S,v)}"value"in y&&r(m,"value",h.value,y.value,S)}},A=(m,h,y,v,S,w,O,R,E)=>{const $=h.el=m?m.el:a(""),K=h.anchor=m?m.anchor:a("");let{patchFlag:L,dynamicChildren:W,slotScopeIds:Y}=h;Y&&(R=R?R.concat(Y):Y),m==null?(n($,y,v),n(K,y,v),G(h.children||[],y,K,S,w,O,R,E)):L>0&&L&64&&W&&m.dynamicChildren?(q(m.dynamicChildren,W,y,S,w,O,R),(h.key!=null||S&&h===S.subTree)&&cr(m,h,!0)):rt(m,h,y,K,S,w,O,R,E)},X=(m,h,y,v,S,w,O,R,E)=>{h.slotScopeIds=R,m==null?h.shapeFlag&512?S.ctx.activate(h,y,v,O,E):ft(h,y,v,S,w,O,E):_t(m,h,E)},ft=(m,h,y,v,S,w,O)=>{const R=m.component=ja(m,v,S);if(Ui(m)&&(R.ctx.renderer=z),Fa(R,!1,O),R.asyncDep){if(S&&S.registerDep(R,ct,O),!m.el){const E=R.subTree=it(fe);B(null,E,h,y)}}else ct(R,m,h,y,S,w,O)},_t=(m,h,y)=>{const v=h.component=m.component;if(Ca(m,h,y))if(v.asyncDep&&!v.asyncResolved){Q(v,h,y);return}else v.next=h,v.update();else h.el=m.el,v.vnode=h},ct=(m,h,y,v,S,w,O)=>{const R=()=>{if(m.isMounted){let{next:L,bu:W,u:Y,parent:st,vnode:pt}=m;{const Yt=ur(m);if(Yt){L&&(L.el=pt.el,Q(m,L,O)),Yt.asyncDep.then(()=>{m.isUnmounted||R()});return}}let ut=L,Rt;Te(m,!1),L?(L.el=pt.el,Q(m,L,O)):L=pt,W&&rn(W),(Rt=L.props&&L.props.onVnodeBeforeUpdate)&&Qt(Rt,st,L,pt),Te(m,!0);const At=_o(m),Gt=m.subTree;m.subTree=At,T(Gt,At,u(Gt.el),_(Gt),m,S,w),L.el=At.el,ut===null&&$a(m,At.el),Y&&Lt(Y,S),(Rt=L.props&&L.props.onVnodeUpdated)&&Lt(()=>Qt(Rt,st,L,pt),S)}else{let L;const{el:W,props:Y}=h,{bm:st,m:pt,parent:ut,root:Rt,type:At}=m,Gt=rs(h);Te(m,!1),st&&rn(st),!Gt&&(L=Y&&Y.onVnodeBeforeMount)&&Qt(L,ut,h),Te(m,!0);{Rt.ce&&Rt.ce._def.shadowRoot!==!1&&Rt.ce._injectChildStyle(At);const Yt=m.subTree=_o(m);T(null,Yt,y,v,m,S,w),h.el=Yt.el}if(pt&&Lt(pt,S),!Gt&&(L=Y&&Y.onVnodeMounted)){const Yt=h;Lt(()=>Qt(L,ut,Yt),S)}(h.shapeFlag&256||ut&&rs(ut.vnode)&&ut.vnode.shapeFlag&256)&&m.a&&Lt(m.a,S),m.isMounted=!0,h=y=v=null}};m.scope.on();const E=m.effect=new _i(R);m.scope.off();const $=m.update=E.run.bind(E),K=m.job=E.runIfDirty.bind(E);K.i=m,K.id=m.uid,E.scheduler=()=>Xn(K),Te(m,!0),$()},Q=(m,h,y)=>{h.component=m;const v=m.vnode.props;m.vnode=h,m.next=null,ca(m,h.props,v,y),pa(m,h.children,y),ue(),po(m),de()},rt=(m,h,y,v,S,w,O,R,E=!1)=>{const $=m&&m.children,K=m?m.shapeFlag:0,L=h.children,{patchFlag:W,shapeFlag:Y}=h;if(W>0){if(W&128){me($,L,y,v,S,w,O,R,E);return}else if(W&256){oe($,L,y,v,S,w,O,R,E);return}}Y&8?(K&16&&Nt($,S,w),L!==$&&f(y,L)):K&16?Y&16?me($,L,y,v,S,w,O,R,E):Nt($,S,w,!0):(K&8&&f(y,""),Y&16&&G(L,y,v,S,w,O,R,E))},oe=(m,h,y,v,S,w,O,R,E)=>{m=m||Ve,h=h||Ve;const $=m.length,K=h.length,L=Math.min($,K);let W;for(W=0;W<L;W++){const Y=h[W]=E?be(h[W]):te(h[W]);T(m[W],Y,y,null,S,w,O,R,E)}$>K?Nt(m,S,w,!0,!1,L):G(h,y,v,S,w,O,R,E,L)},me=(m,h,y,v,S,w,O,R,E)=>{let $=0;const K=h.length;let L=m.length-1,W=K-1;for(;$<=L&&$<=W;){const Y=m[$],st=h[$]=E?be(h[$]):te(h[$]);if(He(Y,st))T(Y,st,y,null,S,w,O,R,E);else break;$++}for(;$<=L&&$<=W;){const Y=m[L],st=h[W]=E?be(h[W]):te(h[W]);if(He(Y,st))T(Y,st,y,null,S,w,O,R,E);else break;L--,W--}if($>L){if($<=W){const Y=W+1,st=Y<K?h[Y].el:v;for(;$<=W;)T(null,h[$]=E?be(h[$]):te(h[$]),y,st,S,w,O,R,E),$++}}else if($>W)for(;$<=L;)Mt(m[$],S,w,!0),$++;else{const Y=$,st=$,pt=new Map;for($=st;$<=W;$++){const jt=h[$]=E?be(h[$]):te(h[$]);jt.key!=null&&pt.set(jt.key,$)}let ut,Rt=0;const At=W-st+1;let Gt=!1,Yt=0;const Je=new Array(At);for($=0;$<At;$++)Je[$]=0;for($=Y;$<=L;$++){const jt=m[$];if(Rt>=At){Mt(jt,S,w,!0);continue}let Jt;if(jt.key!=null)Jt=pt.get(jt.key);else for(ut=st;ut<=W;ut++)if(Je[ut-st]===0&&He(jt,h[ut])){Jt=ut;break}Jt===void 0?Mt(jt,S,w,!0):(Je[Jt-st]=$+1,Jt>=Yt?Yt=Jt:Gt=!0,T(jt,h[Jt],y,null,S,w,O,R,E),Rt++)}const lo=Gt?xa(Je):Ve;for(ut=lo.length-1,$=At-1;$>=0;$--){const jt=st+$,Jt=h[jt],ao=jt+1<K?h[jt+1].el:v;Je[$]===0?T(null,Jt,y,ao,S,w,O,R,E):Gt&&(ut<0||$!==lo[ut]?qt(Jt,y,ao,2):ut--)}}},qt=(m,h,y,v,S=null)=>{const{el:w,type:O,transition:R,children:E,shapeFlag:$}=m;if($&6){qt(m.component.subTree,h,y,v);return}if($&128){m.suspense.move(h,y,v);return}if($&64){O.move(m,h,y,z);return}if(O===gt){n(w,h,y);for(let L=0;L<E.length;L++)qt(E[L],h,y,v);n(m.anchor,h,y);return}if(O===Os){P(m,h,y);return}if(v!==2&&$&1&&R)if(v===0)R.beforeEnter(w),n(w,h,y),Lt(()=>R.enter(w),S);else{const{leave:L,delayLeave:W,afterLeave:Y}=R,st=()=>{m.ctx.isUnmounted?o(w):n(w,h,y)},pt=()=>{L(w,()=>{st(),Y&&Y()})};W?W(w,st,pt):pt()}else n(w,h,y)},Mt=(m,h,y,v=!1,S=!1)=>{const{type:w,props:O,ref:R,children:E,dynamicChildren:$,shapeFlag:K,patchFlag:L,dirs:W,cacheIndex:Y}=m;if(L===-2&&(S=!1),R!=null&&(ue(),is(R,null,y,m,!0),de()),Y!=null&&(h.renderCache[Y]=void 0),K&256){h.ctx.deactivate(m);return}const st=K&1&&W,pt=!rs(m);let ut;if(pt&&(ut=O&&O.onVnodeBeforeUnmount)&&Qt(ut,h,m),K&6)Ps(m.component,y,v);else{if(K&128){m.suspense.unmount(y,v);return}st&&Pe(m,null,h,"beforeUnmount"),K&64?m.type.remove(m,h,y,z,v):$&&!$.hasOnce&&(w!==gt||L>0&&L&64)?Nt($,h,y,!1,!0):(w===gt&&L&384||!S&&K&16)&&Nt(E,h,y),v&&je(m)}(pt&&(ut=O&&O.onVnodeUnmounted)||st)&&Lt(()=>{ut&&Qt(ut,h,m),st&&Pe(m,null,h,"unmounted")},y)},je=m=>{const{type:h,el:y,anchor:v,transition:S}=m;if(h===gt){Le(y,v);return}if(h===Os){M(m);return}const w=()=>{o(y),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(m.shapeFlag&1&&S&&!S.persisted){const{leave:O,delayLeave:R}=S,E=()=>O(y,w);R?R(m.el,w,E):E()}else w()},Le=(m,h)=>{let y;for(;m!==h;)y=d(m),o(m),m=y;o(h)},Ps=(m,h,y)=>{const{bum:v,scope:S,job:w,subTree:O,um:R,m:E,a:$,parent:K,slots:{__:L}}=m;bo(E),bo($),v&&rn(v),K&&U(L)&&L.forEach(W=>{K.renderCache[W]=void 0}),S.stop(),w&&(w.flags|=8,Mt(O,m,h,y)),R&&Lt(R,h),Lt(()=>{m.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&m.asyncDep&&!m.asyncResolved&&m.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},Nt=(m,h,y,v=!1,S=!1,w=0)=>{for(let O=w;O<m.length;O++)Mt(m[O],h,y,v,S)},_=m=>{if(m.shapeFlag&6)return _(m.component.subTree);if(m.shapeFlag&128)return m.suspense.next();const h=d(m.anchor||m.el),y=h&&h[jl];return y?d(y):h};let H=!1;const j=(m,h,y)=>{m==null?h._vnode&&Mt(h._vnode,null,null,!0):T(h._vnode||null,m,h,null,null,null,y),h._vnode=m,H||(H=!0,po(),Hi(),H=!1)},z={p:T,um:Mt,m:qt,r:je,mt:ft,mc:G,pc:rt,pbc:q,n:_,o:t};return{render:j,hydrate:void 0,createApp:ra(j)}}function fn({type:t,props:e},s){return s==="svg"&&t==="foreignObject"||s==="mathml"&&t==="annotation-xml"&&e&&e.encoding&&e.encoding.includes("html")?void 0:s}function Te({effect:t,job:e},s){s?(t.flags|=32,e.flags|=4):(t.flags&=-33,e.flags&=-5)}function ga(t,e){return(!t||t&&!t.pendingBranch)&&e&&!e.persisted}function cr(t,e,s=!1){const n=t.children,o=e.children;if(U(n)&&U(o))for(let r=0;r<n.length;r++){const l=n[r];let a=o[r];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[r]=be(o[r]),a.el=l.el),!s&&a.patchFlag!==-2&&cr(l,a)),a.type===en&&(a.el=l.el),a.type===fe&&!a.el&&(a.el=l.el)}}function xa(t){const e=t.slice(),s=[0];let n,o,r,l,a;const c=t.length;for(n=0;n<c;n++){const p=t[n];if(p!==0){if(o=s[s.length-1],t[o]<p){e[n]=o,s.push(n);continue}for(r=0,l=s.length-1;r<l;)a=r+l>>1,t[s[a]]<p?r=a+1:l=a;p<t[s[r]]&&(r>0&&(e[n]=s[r-1]),s[r]=n)}}for(r=s.length,l=s[r-1];r-- >0;)s[r]=l,l=e[l];return s}function ur(t){const e=t.subTree.component;if(e)return e.asyncDep&&!e.asyncResolved?e:ur(e)}function bo(t){if(t)for(let e=0;e<t.length;e++)t[e].flags|=8}const ya=Symbol.for("v-scx"),va=()=>se(ya);function as(t,e,s){return dr(t,e,s)}function dr(t,e,s=ht){const{immediate:n,deep:o,flush:r,once:l}=s,a=wt({},s),c=e&&n||!e&&r!=="post";let p;if(vs){if(r==="sync"){const g=va();p=g.__watcherHandles||(g.__watcherHandles=[])}else if(!c){const g=()=>{};return g.stop=ee,g.resume=ee,g.pause=ee,g}}const f=kt;a.call=(g,k,T)=>Kt(g,f,k,T);let u=!1;r==="post"?a.scheduler=g=>{Lt(g,f&&f.suspense)}:r!=="sync"&&(u=!0,a.scheduler=(g,k)=>{k?g():Xn(g)}),a.augmentJob=g=>{e&&(g.flags|=4),u&&(g.flags|=2,f&&(g.id=f.uid,g.i=f))};const d=Rl(t,e,a);return vs&&(p?p.push(d):c&&d()),d}function ba(t,e,s){const n=this.proxy,o=bt(t)?t.includes(".")?fr(n,t):()=>n[t]:t.bind(n,n);let r;J(e)?r=e:(r=e.handler,s=e);const l=Ss(this),a=dr(o,r.bind(n),s);return l(),a}function fr(t,e){const s=e.split(".");return()=>{let n=t;for(let o=0;o<s.length&&n;o++)n=n[s[o]];return n}}const _a=(t,e)=>e==="modelValue"||e==="model-value"?t.modelModifiers:t[`${e}Modifiers`]||t[`${Wt(e)}Modifiers`]||t[`${Oe(e)}Modifiers`];function wa(t,e,...s){if(t.isUnmounted)return;const n=t.vnode.props||ht;let o=s;const r=e.startsWith("update:"),l=r&&_a(n,e.slice(7));l&&(l.trim&&(o=s.map(f=>bt(f)?f.trim():f)),l.number&&(o=s.map(Gr)));let a,c=n[a=on(e)]||n[a=on(Wt(e))];!c&&r&&(c=n[a=on(Oe(e))]),c&&Kt(c,t,6,o);const p=n[a+"Once"];if(p){if(!t.emitted)t.emitted={};else if(t.emitted[a])return;t.emitted[a]=!0,Kt(p,t,6,o)}}function pr(t,e,s=!1){const n=e.emitsCache,o=n.get(t);if(o!==void 0)return o;const r=t.emits;let l={},a=!1;if(!J(t)){const c=p=>{const f=pr(p,e,!0);f&&(a=!0,wt(l,f))};!s&&e.mixins.length&&e.mixins.forEach(c),t.extends&&c(t.extends),t.mixins&&t.mixins.forEach(c)}return!r&&!a?(yt(t)&&n.set(t,null),null):(U(r)?r.forEach(c=>l[c]=null):wt(l,r),yt(t)&&n.set(t,l),l)}function tn(t,e){return!t||!Us(e)?!1:(e=e.slice(2).replace(/Once$/,""),at(t,e[0].toLowerCase()+e.slice(1))||at(t,Oe(e))||at(t,e))}function _o(t){const{type:e,vnode:s,proxy:n,withProxy:o,propsOptions:[r],slots:l,attrs:a,emit:c,render:p,renderCache:f,props:u,data:d,setupState:g,ctx:k,inheritAttrs:T}=t,N=Hs(t);let B,C;try{if(s.shapeFlag&4){const M=o||n,V=M;B=te(p.call(V,M,f,u,g,d,k)),C=a}else{const M=e;B=te(M.length>1?M(u,{attrs:a,slots:l,emit:c}):M(u,null)),C=e.props?a:ka(a)}}catch(M){cs.length=0,Xs(M,t,1),B=it(fe)}let P=B;if(C&&T!==!1){const M=Object.keys(C),{shapeFlag:V}=P;M.length&&V&7&&(r&&M.some(Nn)&&(C=Sa(C,r)),P=De(P,C,!1,!0))}return s.dirs&&(P=De(P,null,!1,!0),P.dirs=P.dirs?P.dirs.concat(s.dirs):s.dirs),s.transition&&xs(P,s.transition),B=P,Hs(N),B}const ka=t=>{let e;for(const s in t)(s==="class"||s==="style"||Us(s))&&((e||(e={}))[s]=t[s]);return e},Sa=(t,e)=>{const s={};for(const n in t)(!Nn(n)||!(n.slice(9)in e))&&(s[n]=t[n]);return s};function Ca(t,e,s){const{props:n,children:o,component:r}=t,{props:l,children:a,patchFlag:c}=e,p=r.emitsOptions;if(e.dirs||e.transition)return!0;if(s&&c>=0){if(c&1024)return!0;if(c&16)return n?wo(n,l,p):!!l;if(c&8){const f=e.dynamicProps;for(let u=0;u<f.length;u++){const d=f[u];if(l[d]!==n[d]&&!tn(p,d))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:n===l?!1:n?l?wo(n,l,p):!0:!!l;return!1}function wo(t,e,s){const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!0;for(let o=0;o<n.length;o++){const r=n[o];if(e[r]!==t[r]&&!tn(s,r))return!0}return!1}function $a({vnode:t,parent:e},s){for(;e;){const n=e.subTree;if(n.suspense&&n.suspense.activeBranch===t&&(n.el=t.el),n===t)(t=e.vnode).el=s,e=e.parent;else break}}const mr=t=>t.__isSuspense;function Pa(t,e){e&&e.pendingBranch?U(t)?e.effects.push(...t):e.effects.push(t):Ol(t)}const gt=Symbol.for("v-fgt"),en=Symbol.for("v-txt"),fe=Symbol.for("v-cmt"),Os=Symbol.for("v-stc"),cs=[];let Bt=null;function F(t=!1){cs.push(Bt=t?null:[])}function Ta(){cs.pop(),Bt=cs[cs.length-1]||null}let ys=1;function ko(t,e=!1){ys+=t,t<0&&Bt&&e&&(Bt.hasOnce=!0)}function hr(t){return t.dynamicChildren=ys>0?Bt||Ve:null,Ta(),ys>0&&Bt&&Bt.push(t),t}function I(t,e,s,n,o,r){return hr(i(t,e,s,n,o,r,!0))}function Ea(t,e,s,n,o){return hr(it(t,e,s,n,o,!0))}function Ws(t){return t?t.__v_isVNode===!0:!1}function He(t,e){return t.type===e.type&&t.key===e.key}const gr=({key:t})=>t??null,js=({ref:t,ref_key:e,ref_for:s})=>(typeof t=="number"&&(t=""+t),t!=null?bt(t)||vt(t)||J(t)?{i:It,r:t,k:e,f:!!s}:t:null);function i(t,e=null,s=null,n=0,o=null,r=t===gt?0:1,l=!1,a=!1){const c={__v_isVNode:!0,__v_skip:!0,type:t,props:e,key:e&&gr(e),ref:e&&js(e),scopeId:Wi,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:n,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:It};return a?(eo(c,s),r&128&&t.normalize(c)):s&&(c.shapeFlag|=bt(s)?8:16),ys>0&&!l&&Bt&&(c.patchFlag>0||r&6)&&c.patchFlag!==32&&Bt.push(c),c}const it=Ma;function Ma(t,e=null,s=null,n=0,o=null,r=!1){if((!t||t===Ql)&&(t=fe),Ws(t)){const a=De(t,e,!0);return s&&eo(a,s),ys>0&&!r&&Bt&&(a.shapeFlag&6?Bt[Bt.indexOf(t)]=a:Bt.push(a)),a.patchFlag=-2,a}if(za(t)&&(t=t.__vccOpts),e){e=Ra(e);let{class:a,style:c}=e;a&&!bt(a)&&(e.class=Ft(a)),yt(c)&&(Yn(c)&&!U(c)&&(c=wt({},c)),e.style=ps(c))}const l=bt(t)?1:mr(t)?128:Ll(t)?64:yt(t)?4:J(t)?2:0;return i(t,e,s,n,o,l,r,!0)}function Ra(t){return t?Yn(t)||nr(t)?wt({},t):t:null}function De(t,e,s=!1,n=!1){const{props:o,ref:r,patchFlag:l,children:a,transition:c}=t,p=e?Aa(o||{},e):o,f={__v_isVNode:!0,__v_skip:!0,type:t.type,props:p,key:p&&gr(p),ref:e&&e.ref?s&&r?U(r)?r.concat(js(e)):[r,js(e)]:js(e):r,scopeId:t.scopeId,slotScopeIds:t.slotScopeIds,children:a,target:t.target,targetStart:t.targetStart,targetAnchor:t.targetAnchor,staticCount:t.staticCount,shapeFlag:t.shapeFlag,patchFlag:e&&t.type!==gt?l===-1?16:l|16:l,dynamicProps:t.dynamicProps,dynamicChildren:t.dynamicChildren,appContext:t.appContext,dirs:t.dirs,transition:c,component:t.component,suspense:t.suspense,ssContent:t.ssContent&&De(t.ssContent),ssFallback:t.ssFallback&&De(t.ssFallback),el:t.el,anchor:t.anchor,ctx:t.ctx,ce:t.ce};return c&&n&&xs(f,c.clone(f)),f}function es(t=" ",e=0){return it(en,null,t,e)}function ce(t,e){const s=it(Os,null,t);return s.staticCount=e,s}function ot(t="",e=!1){return e?(F(),Ea(fe,null,t)):it(fe,null,t)}function te(t){return t==null||typeof t=="boolean"?it(fe):U(t)?it(gt,null,t.slice()):Ws(t)?be(t):it(en,null,String(t))}function be(t){return t.el===null&&t.patchFlag!==-1||t.memo?t:De(t)}function eo(t,e){let s=0;const{shapeFlag:n}=t;if(e==null)e=null;else if(U(e))s=16;else if(typeof e=="object")if(n&65){const o=e.default;o&&(o._c&&(o._d=!1),eo(t,o()),o._c&&(o._d=!0));return}else{s=32;const o=e._;!o&&!nr(e)?e._ctx=It:o===3&&It&&(It.slots._===1?e._=1:(e._=2,t.patchFlag|=1024))}else J(e)?(e={default:e,_ctx:It},s=32):(e=String(e),n&64?(s=16,e=[es(e)]):s=8);t.children=e,t.shapeFlag|=s}function Aa(...t){const e={};for(let s=0;s<t.length;s++){const n=t[s];for(const o in n)if(o==="class")e.class!==n.class&&(e.class=Ft([e.class,n.class]));else if(o==="style")e.style=ps([e.style,n.style]);else if(Us(o)){const r=e[o],l=n[o];l&&r!==l&&!(U(r)&&r.includes(l))&&(e[o]=r?[].concat(r,l):l)}else o!==""&&(e[o]=n[o])}return e}function Qt(t,e,s,n=null){Kt(t,e,7,[s,n])}const Da=tr();let Oa=0;function ja(t,e,s){const n=t.type,o=(e?e.appContext:t.appContext)||Da,r={uid:Oa++,vnode:t,type:n,parent:e,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new yi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:e?e.provides:Object.create(o.provides),ids:e?e.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ir(n,o),emitsOptions:pr(n,o),emit:null,emitted:null,propsDefaults:ht,inheritAttrs:n.inheritAttrs,ctx:ht,data:ht,props:ht,attrs:ht,slots:ht,refs:ht,setupState:ht,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=e?e.root:r,r.emit=wa.bind(null,r),t.ce&&t.ce(r),r}let kt=null;const La=()=>kt||It;let Vs,Rn;{const t=Js(),e=(s,n)=>{let o;return(o=t[s])||(o=t[s]=[]),o.push(n),r=>{o.length>1?o.forEach(l=>l(r)):o[0](r)}};Vs=e("__VUE_INSTANCE_SETTERS__",s=>kt=s),Rn=e("__VUE_SSR_SETTERS__",s=>vs=s)}const Ss=t=>{const e=kt;return Vs(t),t.scope.on(),()=>{t.scope.off(),Vs(e)}},So=()=>{kt&&kt.scope.off(),Vs(null)};function xr(t){return t.vnode.shapeFlag&4}let vs=!1;function Fa(t,e=!1,s=!1){e&&Rn(e);const{props:n,children:o}=t.vnode,r=xr(t);aa(t,n,r,e),fa(t,o,s||e);const l=r?Ia(t,e):void 0;return e&&Rn(!1),l}function Ia(t,e){const s=t.type;t.accessCache=Object.create(null),t.proxy=new Proxy(t.ctx,Zl);const{setup:n}=s;if(n){ue();const o=t.setupContext=n.length>1?Na(t):null,r=Ss(t),l=ks(n,t,0,[t.props,o]),a=fi(l);if(de(),r(),(a||t.sp)&&!rs(t)&&Ki(t),a){if(l.then(So,So),e)return l.then(c=>{Co(t,c)}).catch(c=>{Xs(c,t,0)});t.asyncDep=l}else Co(t,l)}else yr(t)}function Co(t,e,s){J(e)?t.type.__ssrInlineRender?t.ssrRender=e:t.render=e:yt(e)&&(t.setupState=Ii(e)),yr(t)}function yr(t,e,s){const n=t.type;t.render||(t.render=n.render||ee);{const o=Ss(t);ue();try{ta(t)}finally{de(),o()}}}const Ba={get(t,e){return Pt(t,"get",""),t[e]}};function Na(t){const e=s=>{t.exposed=s||{}};return{attrs:new Proxy(t.attrs,Ba),slots:t.slots,emit:t.emit,expose:e}}function so(t){return t.exposed?t.exposeProxy||(t.exposeProxy=new Proxy(Ii(Jn(t.exposed)),{get(e,s){if(s in e)return e[s];if(s in ls)return ls[s](t)},has(e,s){return s in e||s in ls}})):t.proxy}function Ha(t,e=!0){return J(t)?t.displayName||t.name:t.name||e&&t.__name}function za(t){return J(t)&&"__vccOpts"in t}const xt=(t,e)=>El(t,e,vs);function vr(t,e,s){const n=arguments.length;return n===2?yt(e)&&!U(e)?Ws(e)?it(t,null,[e]):it(t,e):it(t,null,e):(n>3?s=Array.prototype.slice.call(arguments,2):n===3&&Ws(s)&&(s=[s]),it(t,e,s))}const Wa="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let An;const $o=typeof window<"u"&&window.trustedTypes;if($o)try{An=$o.createPolicy("vue",{createHTML:t=>t})}catch{}const br=An?t=>An.createHTML(t):t=>t,Va="http://www.w3.org/2000/svg",Ka="http://www.w3.org/1998/Math/MathML",le=typeof document<"u"?document:null,Po=le&&le.createElement("template"),Ua={insert:(t,e,s)=>{e.insertBefore(t,s||null)},remove:t=>{const e=t.parentNode;e&&e.removeChild(t)},createElement:(t,e,s,n)=>{const o=e==="svg"?le.createElementNS(Va,t):e==="mathml"?le.createElementNS(Ka,t):s?le.createElement(t,{is:s}):le.createElement(t);return t==="select"&&n&&n.multiple!=null&&o.setAttribute("multiple",n.multiple),o},createText:t=>le.createTextNode(t),createComment:t=>le.createComment(t),setText:(t,e)=>{t.nodeValue=e},setElementText:(t,e)=>{t.textContent=e},parentNode:t=>t.parentNode,nextSibling:t=>t.nextSibling,querySelector:t=>le.querySelector(t),setScopeId(t,e){t.setAttribute(e,"")},insertStaticContent(t,e,s,n,o,r){const l=s?s.previousSibling:e.lastChild;if(o&&(o===r||o.nextSibling))for(;e.insertBefore(o.cloneNode(!0),s),!(o===r||!(o=o.nextSibling)););else{Po.innerHTML=br(n==="svg"?`<svg>${t}</svg>`:n==="mathml"?`<math>${t}</math>`:t);const a=Po.content;if(n==="svg"||n==="mathml"){const c=a.firstChild;for(;c.firstChild;)a.appendChild(c.firstChild);a.removeChild(c)}e.insertBefore(a,s)}return[l?l.nextSibling:e.firstChild,s?s.previousSibling:e.lastChild]}},he="transition",Xe="animation",qe=Symbol("_vtc"),_r={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},qa=wt({},Il,_r),Ee=(t,e=[])=>{U(t)?t.forEach(s=>s(...e)):t&&t(...e)},To=t=>t?U(t)?t.some(e=>e.length>1):t.length>1:!1;function Ga(t){const e={};for(const A in t)A in _r||(e[A]=t[A]);if(t.css===!1)return e;const{name:s="v",type:n,duration:o,enterFromClass:r=`${s}-enter-from`,enterActiveClass:l=`${s}-enter-active`,enterToClass:a=`${s}-enter-to`,appearFromClass:c=r,appearActiveClass:p=l,appearToClass:f=a,leaveFromClass:u=`${s}-leave-from`,leaveActiveClass:d=`${s}-leave-active`,leaveToClass:g=`${s}-leave-to`}=t,k=Ya(o),T=k&&k[0],N=k&&k[1],{onBeforeEnter:B,onEnter:C,onEnterCancelled:P,onLeave:M,onLeaveCancelled:V,onBeforeAppear:Z=B,onAppear:et=C,onAppearCancelled:G=P}=e,D=(A,X,ft,_t)=>{A._enterCancelled=_t,xe(A,X?f:a),xe(A,X?p:l),ft&&ft()},q=(A,X)=>{A._isLeaving=!1,xe(A,u),xe(A,g),xe(A,d),X&&X()},tt=A=>(X,ft)=>{const _t=A?et:C,ct=()=>D(X,A,ft);Ee(_t,[X,ct]),Eo(()=>{xe(X,A?c:r),Xt(X,A?f:a),To(_t)||Mo(X,n,T,ct)})};return wt(e,{onBeforeEnter(A){Ee(B,[A]),Xt(A,r),Xt(A,l)},onBeforeAppear(A){Ee(Z,[A]),Xt(A,c),Xt(A,p)},onEnter:tt(!1),onAppear:tt(!0),onLeave(A,X){A._isLeaving=!0;const ft=()=>q(A,X);Xt(A,u),A._enterCancelled?(Xt(A,d),Dn()):(Dn(),Xt(A,d)),Eo(()=>{A._isLeaving&&(xe(A,u),Xt(A,g),To(M)||Mo(A,n,N,ft))}),Ee(M,[A,ft])},onEnterCancelled(A){D(A,!1,void 0,!0),Ee(P,[A])},onAppearCancelled(A){D(A,!0,void 0,!0),Ee(G,[A])},onLeaveCancelled(A){q(A),Ee(V,[A])}})}function Ya(t){if(t==null)return null;if(yt(t))return[pn(t.enter),pn(t.leave)];{const e=pn(t);return[e,e]}}function pn(t){return Yr(t)}function Xt(t,e){e.split(/\s+/).forEach(s=>s&&t.classList.add(s)),(t[qe]||(t[qe]=new Set)).add(e)}function xe(t,e){e.split(/\s+/).forEach(n=>n&&t.classList.remove(n));const s=t[qe];s&&(s.delete(e),s.size||(t[qe]=void 0))}function Eo(t){requestAnimationFrame(()=>{requestAnimationFrame(t)})}let Ja=0;function Mo(t,e,s,n){const o=t._endId=++Ja,r=()=>{o===t._endId&&n()};if(s!=null)return setTimeout(r,s);const{type:l,timeout:a,propCount:c}=wr(t,e);if(!l)return n();const p=l+"end";let f=0;const u=()=>{t.removeEventListener(p,d),r()},d=g=>{g.target===t&&++f>=c&&u()};setTimeout(()=>{f<c&&u()},a+1),t.addEventListener(p,d)}function wr(t,e){const s=window.getComputedStyle(t),n=k=>(s[k]||"").split(", "),o=n(`${he}Delay`),r=n(`${he}Duration`),l=Ro(o,r),a=n(`${Xe}Delay`),c=n(`${Xe}Duration`),p=Ro(a,c);let f=null,u=0,d=0;e===he?l>0&&(f=he,u=l,d=r.length):e===Xe?p>0&&(f=Xe,u=p,d=c.length):(u=Math.max(l,p),f=u>0?l>p?he:Xe:null,d=f?f===he?r.length:c.length:0);const g=f===he&&/\b(transform|all)(,|$)/.test(n(`${he}Property`).toString());return{type:f,timeout:u,propCount:d,hasTransform:g}}function Ro(t,e){for(;t.length<e.length;)t=t.concat(t);return Math.max(...e.map((s,n)=>Ao(s)+Ao(t[n])))}function Ao(t){return t==="auto"?0:Number(t.slice(0,-1).replace(",","."))*1e3}function Dn(){return document.body.offsetHeight}function Qa(t,e,s){const n=t[qe];n&&(e=(e?[e,...n]:[...n]).join(" ")),e==null?t.removeAttribute("class"):s?t.setAttribute("class",e):t.className=e}const Do=Symbol("_vod"),Xa=Symbol("_vsh"),Za=Symbol(""),tc=/(^|;)\s*display\s*:/;function ec(t,e,s){const n=t.style,o=bt(s);let r=!1;if(s&&!o){if(e)if(bt(e))for(const l of e.split(";")){const a=l.slice(0,l.indexOf(":")).trim();s[a]==null&&Ls(n,a,"")}else for(const l in e)s[l]==null&&Ls(n,l,"");for(const l in s)l==="display"&&(r=!0),Ls(n,l,s[l])}else if(o){if(e!==s){const l=n[Za];l&&(s+=";"+l),n.cssText=s,r=tc.test(s)}}else e&&t.removeAttribute("style");Do in t&&(t[Do]=r?n.display:"",t[Xa]&&(n.display="none"))}const Oo=/\s*!important$/;function Ls(t,e,s){if(U(s))s.forEach(n=>Ls(t,e,n));else if(s==null&&(s=""),e.startsWith("--"))t.setProperty(e,s);else{const n=sc(t,e);Oo.test(s)?t.setProperty(Oe(n),s.replace(Oo,""),"important"):t[n]=s}}const jo=["Webkit","Moz","ms"],mn={};function sc(t,e){const s=mn[e];if(s)return s;let n=Wt(e);if(n!=="filter"&&n in t)return mn[e]=n;n=Ys(n);for(let o=0;o<jo.length;o++){const r=jo[o]+n;if(r in t)return mn[e]=r}return e}const Lo="http://www.w3.org/1999/xlink";function Fo(t,e,s,n,o,r=el(e)){n&&e.startsWith("xlink:")?s==null?t.removeAttributeNS(Lo,e.slice(6,e.length)):t.setAttributeNS(Lo,e,s):s==null||r&&!hi(s)?t.removeAttribute(e):t.setAttribute(e,r?"":$e(s)?String(s):s)}function Io(t,e,s,n,o){if(e==="innerHTML"||e==="textContent"){s!=null&&(t[e]=e==="innerHTML"?br(s):s);return}const r=t.tagName;if(e==="value"&&r!=="PROGRESS"&&!r.includes("-")){const a=r==="OPTION"?t.getAttribute("value")||"":t.value,c=s==null?t.type==="checkbox"?"on":"":String(s);(a!==c||!("_value"in t))&&(t.value=c),s==null&&t.removeAttribute(e),t._value=s;return}let l=!1;if(s===""||s==null){const a=typeof t[e];a==="boolean"?s=hi(s):s==null&&a==="string"?(s="",l=!0):a==="number"&&(s=0,l=!0)}try{t[e]=s}catch{}l&&t.removeAttribute(o||e)}function nc(t,e,s,n){t.addEventListener(e,s,n)}function oc(t,e,s,n){t.removeEventListener(e,s,n)}const Bo=Symbol("_vei");function ic(t,e,s,n,o=null){const r=t[Bo]||(t[Bo]={}),l=r[e];if(n&&l)l.value=n;else{const[a,c]=rc(e);if(n){const p=r[e]=cc(n,o);nc(t,a,p,c)}else l&&(oc(t,a,l,c),r[e]=void 0)}}const No=/(?:Once|Passive|Capture)$/;function rc(t){let e;if(No.test(t)){e={};let n;for(;n=t.match(No);)t=t.slice(0,t.length-n[0].length),e[n[0].toLowerCase()]=!0}return[t[2]===":"?t.slice(3):Oe(t.slice(2)),e]}let hn=0;const lc=Promise.resolve(),ac=()=>hn||(lc.then(()=>hn=0),hn=Date.now());function cc(t,e){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;Kt(uc(n,s.value),e,5,[n])};return s.value=t,s.attached=ac(),s}function uc(t,e){if(U(e)){const s=t.stopImmediatePropagation;return t.stopImmediatePropagation=()=>{s.call(t),t._stopped=!0},e.map(n=>o=>!o._stopped&&n&&n(o))}else return e}const Ho=t=>t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&t.charCodeAt(2)>96&&t.charCodeAt(2)<123,dc=(t,e,s,n,o,r)=>{const l=o==="svg";e==="class"?Qa(t,n,l):e==="style"?ec(t,s,n):Us(e)?Nn(e)||ic(t,e,s,n,r):(e[0]==="."?(e=e.slice(1),!0):e[0]==="^"?(e=e.slice(1),!1):fc(t,e,n,l))?(Io(t,e,n),!t.tagName.includes("-")&&(e==="value"||e==="checked"||e==="selected")&&Fo(t,e,n,l,r,e!=="value")):t._isVueCE&&(/[A-Z]/.test(e)||!bt(n))?Io(t,Wt(e),n,r,e):(e==="true-value"?t._trueValue=n:e==="false-value"&&(t._falseValue=n),Fo(t,e,n,l))};function fc(t,e,s,n){if(n)return!!(e==="innerHTML"||e==="textContent"||e in t&&Ho(e)&&J(s));if(e==="spellcheck"||e==="draggable"||e==="translate"||e==="autocorrect"||e==="form"||e==="list"&&t.tagName==="INPUT"||e==="type"&&t.tagName==="TEXTAREA")return!1;if(e==="width"||e==="height"){const o=t.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return Ho(e)&&bt(s)?!1:e in t}const kr=new WeakMap,Sr=new WeakMap,Ks=Symbol("_moveCb"),zo=Symbol("_enterCb"),pc=t=>(delete t.props.mode,t),mc=pc({name:"TransitionGroup",props:wt({},qa,{tag:String,moveClass:String}),setup(t,{slots:e}){const s=La(),n=Fl();let o,r;return Yi(()=>{if(!o.length)return;const l=t.moveClass||`${t.name||"v"}-move`;if(!vc(o[0].el,s.vnode.el,l)){o=[];return}o.forEach(gc),o.forEach(xc);const a=o.filter(yc);Dn(),a.forEach(c=>{const p=c.el,f=p.style;Xt(p,l),f.transform=f.webkitTransform=f.transitionDuration="";const u=p[Ks]=d=>{d&&d.target!==p||(!d||/transform$/.test(d.propertyName))&&(p.removeEventListener("transitionend",u),p[Ks]=null,xe(p,l))};p.addEventListener("transitionend",u)}),o=[]}),()=>{const l=nt(t),a=Ga(l);let c=l.tag||gt;if(o=[],r)for(let p=0;p<r.length;p++){const f=r[p];f.el&&f.el instanceof Element&&(o.push(f),xs(f,$n(f,a,n,s)),kr.set(f,f.el.getBoundingClientRect()))}r=e.default?Vi(e.default()):[];for(let p=0;p<r.length;p++){const f=r[p];f.key!=null&&xs(f,$n(f,a,n,s))}return it(c,null,r)}}}),hc=mc;function gc(t){const e=t.el;e[Ks]&&e[Ks](),e[zo]&&e[zo]()}function xc(t){Sr.set(t,t.el.getBoundingClientRect())}function yc(t){const e=kr.get(t),s=Sr.get(t),n=e.left-s.left,o=e.top-s.top;if(n||o){const r=t.el.style;return r.transform=r.webkitTransform=`translate(${n}px,${o}px)`,r.transitionDuration="0s",t}}function vc(t,e,s){const n=t.cloneNode(),o=t[qe];o&&o.forEach(a=>{a.split(/\s+/).forEach(c=>c&&n.classList.remove(c))}),s.split(/\s+/).forEach(a=>a&&n.classList.add(a)),n.style.display="none";const r=e.nodeType===1?e:e.parentNode;r.appendChild(n);const{hasTransform:l}=wr(n);return r.removeChild(n),l}const bc=wt({patchProp:dc},Ua);let Wo;function _c(){return Wo||(Wo=ma(bc))}const wc=(...t)=>{const e=_c().createApp(...t),{mount:s}=e;return e.mount=n=>{const o=Sc(n);if(!o)return;const r=e._component;!J(r)&&!r.render&&!r.template&&(r.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const l=s(o,!1,kc(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),l},e};function kc(t){if(t instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&t instanceof MathMLElement)return"mathml"}function Sc(t){return bt(t)?document.querySelector(t):t}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Cr;const sn=t=>Cr=t,$r=Symbol();function On(t){return t&&typeof t=="object"&&Object.prototype.toString.call(t)==="[object Object]"&&typeof t.toJSON!="function"}var us;(function(t){t.direct="direct",t.patchObject="patch object",t.patchFunction="patch function"})(us||(us={}));function Cc(){const t=vi(!0),e=t.run(()=>Ct({}));let s=[],n=[];const o=Jn({install(r){sn(o),o._a=r,r.provide($r,o),r.config.globalProperties.$pinia=o,n.forEach(l=>s.push(l)),n=[]},use(r){return this._a?s.push(r):n.push(r),this},_p:s,_a:null,_e:t,_s:new Map,state:e});return o}const Pr=()=>{};function Vo(t,e,s,n=Pr){t.push(e);const o=()=>{const r=t.indexOf(e);r>-1&&(t.splice(r,1),n())};return!s&&bi()&&sl(o),o}function Be(t,...e){t.slice().forEach(s=>{s(...e)})}const $c=t=>t(),Ko=Symbol(),gn=Symbol();function jn(t,e){t instanceof Map&&e instanceof Map?e.forEach((s,n)=>t.set(n,s)):t instanceof Set&&e instanceof Set&&e.forEach(t.add,t);for(const s in e){if(!e.hasOwnProperty(s))continue;const n=e[s],o=t[s];On(o)&&On(n)&&t.hasOwnProperty(s)&&!vt(n)&&!Se(n)?t[s]=jn(o,n):t[s]=n}return t}const Pc=Symbol();function Tc(t){return!On(t)||!Object.prototype.hasOwnProperty.call(t,Pc)}const{assign:ye}=Object;function Ec(t){return!!(vt(t)&&t.effect)}function Mc(t,e,s,n){const{state:o,actions:r,getters:l}=e,a=s.state.value[t];let c;function p(){a||(s.state.value[t]=o?o():{});const f=Cl(s.state.value[t]);return ye(f,r,Object.keys(l||{}).reduce((u,d)=>(u[d]=Jn(xt(()=>{sn(s);const g=s._s.get(t);return l[d].call(g,g)})),u),{}))}return c=Tr(t,p,e,s,n,!0),c}function Tr(t,e,s={},n,o,r){let l;const a=ye({actions:{}},s),c={deep:!0};let p,f,u=[],d=[],g;const k=n.state.value[t];!r&&!k&&(n.state.value[t]={}),Ct({});let T;function N(G){let D;p=f=!1,typeof G=="function"?(G(n.state.value[t]),D={type:us.patchFunction,storeId:t,events:g}):(jn(n.state.value[t],G),D={type:us.patchObject,payload:G,storeId:t,events:g});const q=T=Symbol();Qn().then(()=>{T===q&&(p=!0)}),f=!0,Be(u,D,n.state.value[t])}const B=r?function(){const{state:D}=s,q=D?D():{};this.$patch(tt=>{ye(tt,q)})}:Pr;function C(){l.stop(),u=[],d=[],n._s.delete(t)}const P=(G,D="")=>{if(Ko in G)return G[gn]=D,G;const q=function(){sn(n);const tt=Array.from(arguments),A=[],X=[];function ft(Q){A.push(Q)}function _t(Q){X.push(Q)}Be(d,{args:tt,name:q[gn],store:V,after:ft,onError:_t});let ct;try{ct=G.apply(this&&this.$id===t?this:V,tt)}catch(Q){throw Be(X,Q),Q}return ct instanceof Promise?ct.then(Q=>(Be(A,Q),Q)).catch(Q=>(Be(X,Q),Promise.reject(Q))):(Be(A,ct),ct)};return q[Ko]=!0,q[gn]=D,q},M={_p:n,$id:t,$onAction:Vo.bind(null,d),$patch:N,$reset:B,$subscribe(G,D={}){const q=Vo(u,G,D.detached,()=>tt()),tt=l.run(()=>as(()=>n.state.value[t],A=>{(D.flush==="sync"?f:p)&&G({storeId:t,type:us.direct,events:g},A)},ye({},c,D)));return q},$dispose:C},V=ws(M);n._s.set(t,V);const et=(n._a&&n._a.runWithContext||$c)(()=>n._e.run(()=>(l=vi()).run(()=>e({action:P}))));for(const G in et){const D=et[G];if(vt(D)&&!Ec(D)||Se(D))r||(k&&Tc(D)&&(vt(D)?D.value=k[G]:jn(D,k[G])),n.state.value[t][G]=D);else if(typeof D=="function"){const q=P(D,G);et[G]=q,a.actions[G]=D}}return ye(V,et),ye(nt(V),et),Object.defineProperty(V,"$state",{get:()=>n.state.value[t],set:G=>{N(D=>{ye(D,G)})}}),n._p.forEach(G=>{ye(V,l.run(()=>G({store:V,app:n._a,pinia:n,options:a})))}),k&&r&&s.hydrate&&s.hydrate(V.$state,k),p=!0,f=!0,V}/*! #__NO_SIDE_EFFECTS__ */function no(t,e,s){let n;const o=typeof e=="function";n=o?s:e;function r(l,a){const c=la();return l=l||(c?se($r,null):null),l&&sn(l),l=Cr,l._s.has(t)||(o?Tr(t,e,n,l):Mc(t,n,l)),l._s.get(t)}return r.$id=t,r}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const ze=typeof document<"u";function Er(t){return typeof t=="object"||"displayName"in t||"props"in t||"__vccOpts"in t}function Rc(t){return t.__esModule||t[Symbol.toStringTag]==="Module"||t.default&&Er(t.default)}const lt=Object.assign;function xn(t,e){const s={};for(const n in e){const o=e[n];s[n]=Ut(o)?o.map(t):t(o)}return s}const ds=()=>{},Ut=Array.isArray,Mr=/#/g,Ac=/&/g,Dc=/\//g,Oc=/=/g,jc=/\?/g,Rr=/\+/g,Lc=/%5B/g,Fc=/%5D/g,Ar=/%5E/g,Ic=/%60/g,Dr=/%7B/g,Bc=/%7C/g,Or=/%7D/g,Nc=/%20/g;function oo(t){return encodeURI(""+t).replace(Bc,"|").replace(Lc,"[").replace(Fc,"]")}function Hc(t){return oo(t).replace(Dr,"{").replace(Or,"}").replace(Ar,"^")}function Ln(t){return oo(t).replace(Rr,"%2B").replace(Nc,"+").replace(Mr,"%23").replace(Ac,"%26").replace(Ic,"`").replace(Dr,"{").replace(Or,"}").replace(Ar,"^")}function zc(t){return Ln(t).replace(Oc,"%3D")}function Wc(t){return oo(t).replace(Mr,"%23").replace(jc,"%3F")}function Vc(t){return t==null?"":Wc(t).replace(Dc,"%2F")}function bs(t){try{return decodeURIComponent(""+t)}catch{}return""+t}const Kc=/\/$/,Uc=t=>t.replace(Kc,"");function yn(t,e,s="/"){let n,o={},r="",l="";const a=e.indexOf("#");let c=e.indexOf("?");return a<c&&a>=0&&(c=-1),c>-1&&(n=e.slice(0,c),r=e.slice(c+1,a>-1?a:e.length),o=t(r)),a>-1&&(n=n||e.slice(0,a),l=e.slice(a,e.length)),n=Jc(n??e,s),{fullPath:n+(r&&"?")+r+l,path:n,query:o,hash:bs(l)}}function qc(t,e){const s=e.query?t(e.query):"";return e.path+(s&&"?")+s+(e.hash||"")}function Uo(t,e){return!e||!t.toLowerCase().startsWith(e.toLowerCase())?t:t.slice(e.length)||"/"}function Gc(t,e,s){const n=e.matched.length-1,o=s.matched.length-1;return n>-1&&n===o&&Ge(e.matched[n],s.matched[o])&&jr(e.params,s.params)&&t(e.query)===t(s.query)&&e.hash===s.hash}function Ge(t,e){return(t.aliasOf||t)===(e.aliasOf||e)}function jr(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(!Yc(t[s],e[s]))return!1;return!0}function Yc(t,e){return Ut(t)?qo(t,e):Ut(e)?qo(e,t):t===e}function qo(t,e){return Ut(e)?t.length===e.length&&t.every((s,n)=>s===e[n]):t.length===1&&t[0]===e}function Jc(t,e){if(t.startsWith("/"))return t;if(!t)return e;const s=e.split("/"),n=t.split("/"),o=n[n.length-1];(o===".."||o===".")&&n.push("");let r=s.length-1,l,a;for(l=0;l<n.length;l++)if(a=n[l],a!==".")if(a==="..")r>1&&r--;else break;return s.slice(0,r).join("/")+"/"+n.slice(l).join("/")}const ge={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var _s;(function(t){t.pop="pop",t.push="push"})(_s||(_s={}));var fs;(function(t){t.back="back",t.forward="forward",t.unknown=""})(fs||(fs={}));function Qc(t){if(!t)if(ze){const e=document.querySelector("base");t=e&&e.getAttribute("href")||"/",t=t.replace(/^\w+:\/\/[^\/]+/,"")}else t="/";return t[0]!=="/"&&t[0]!=="#"&&(t="/"+t),Uc(t)}const Xc=/^[^#]+#/;function Zc(t,e){return t.replace(Xc,"#")+e}function tu(t,e){const s=document.documentElement.getBoundingClientRect(),n=t.getBoundingClientRect();return{behavior:e.behavior,left:n.left-s.left-(e.left||0),top:n.top-s.top-(e.top||0)}}const nn=()=>({left:window.scrollX,top:window.scrollY});function eu(t){let e;if("el"in t){const s=t.el,n=typeof s=="string"&&s.startsWith("#"),o=typeof s=="string"?n?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!o)return;e=tu(o,t)}else e=t;"scrollBehavior"in document.documentElement.style?window.scrollTo(e):window.scrollTo(e.left!=null?e.left:window.scrollX,e.top!=null?e.top:window.scrollY)}function Go(t,e){return(history.state?history.state.position-e:-1)+t}const Fn=new Map;function su(t,e){Fn.set(t,e)}function nu(t){const e=Fn.get(t);return Fn.delete(t),e}let ou=()=>location.protocol+"//"+location.host;function Lr(t,e){const{pathname:s,search:n,hash:o}=e,r=t.indexOf("#");if(r>-1){let a=o.includes(t.slice(r))?t.slice(r).length:1,c=o.slice(a);return c[0]!=="/"&&(c="/"+c),Uo(c,"")}return Uo(s,t)+n+o}function iu(t,e,s,n){let o=[],r=[],l=null;const a=({state:d})=>{const g=Lr(t,location),k=s.value,T=e.value;let N=0;if(d){if(s.value=g,e.value=d,l&&l===k){l=null;return}N=T?d.position-T.position:0}else n(g);o.forEach(B=>{B(s.value,k,{delta:N,type:_s.pop,direction:N?N>0?fs.forward:fs.back:fs.unknown})})};function c(){l=s.value}function p(d){o.push(d);const g=()=>{const k=o.indexOf(d);k>-1&&o.splice(k,1)};return r.push(g),g}function f(){const{history:d}=window;d.state&&d.replaceState(lt({},d.state,{scroll:nn()}),"")}function u(){for(const d of r)d();r=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",f)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",f,{passive:!0}),{pauseListeners:c,listen:p,destroy:u}}function Yo(t,e,s,n=!1,o=!1){return{back:t,current:e,forward:s,replaced:n,position:window.history.length,scroll:o?nn():null}}function ru(t){const{history:e,location:s}=window,n={value:Lr(t,s)},o={value:e.state};o.value||r(n.value,{back:null,current:n.value,forward:null,position:e.length-1,replaced:!0,scroll:null},!0);function r(c,p,f){const u=t.indexOf("#"),d=u>-1?(s.host&&document.querySelector("base")?t:t.slice(u))+c:ou()+t+c;try{e[f?"replaceState":"pushState"](p,"",d),o.value=p}catch(g){console.error(g),s[f?"replace":"assign"](d)}}function l(c,p){const f=lt({},e.state,Yo(o.value.back,c,o.value.forward,!0),p,{position:o.value.position});r(c,f,!0),n.value=c}function a(c,p){const f=lt({},o.value,e.state,{forward:c,scroll:nn()});r(f.current,f,!0);const u=lt({},Yo(n.value,c,null),{position:f.position+1},p);r(c,u,!1),n.value=c}return{location:n,state:o,push:a,replace:l}}function lu(t){t=Qc(t);const e=ru(t),s=iu(t,e.state,e.location,e.replace);function n(r,l=!0){l||s.pauseListeners(),history.go(r)}const o=lt({location:"",base:t,go:n,createHref:Zc.bind(null,t)},e,s);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>e.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>e.state.value}),o}function au(t){return typeof t=="string"||t&&typeof t=="object"}function Fr(t){return typeof t=="string"||typeof t=="symbol"}const Ir=Symbol("");var Jo;(function(t){t[t.aborted=4]="aborted",t[t.cancelled=8]="cancelled",t[t.duplicated=16]="duplicated"})(Jo||(Jo={}));function Ye(t,e){return lt(new Error,{type:t,[Ir]:!0},e)}function re(t,e){return t instanceof Error&&Ir in t&&(e==null||!!(t.type&e))}const Qo="[^/]+?",cu={sensitive:!1,strict:!1,start:!0,end:!0},uu=/[.+*?^${}()[\]/\\]/g;function du(t,e){const s=lt({},cu,e),n=[];let o=s.start?"^":"";const r=[];for(const p of t){const f=p.length?[]:[90];s.strict&&!p.length&&(o+="/");for(let u=0;u<p.length;u++){const d=p[u];let g=40+(s.sensitive?.25:0);if(d.type===0)u||(o+="/"),o+=d.value.replace(uu,"\\$&"),g+=40;else if(d.type===1){const{value:k,repeatable:T,optional:N,regexp:B}=d;r.push({name:k,repeatable:T,optional:N});const C=B||Qo;if(C!==Qo){g+=10;try{new RegExp(`(${C})`)}catch(M){throw new Error(`Invalid custom RegExp for param "${k}" (${C}): `+M.message)}}let P=T?`((?:${C})(?:/(?:${C}))*)`:`(${C})`;u||(P=N&&p.length<2?`(?:/${P})`:"/"+P),N&&(P+="?"),o+=P,g+=20,N&&(g+=-8),T&&(g+=-20),C===".*"&&(g+=-50)}f.push(g)}n.push(f)}if(s.strict&&s.end){const p=n.length-1;n[p][n[p].length-1]+=.7000000000000001}s.strict||(o+="/?"),s.end?o+="$":s.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const l=new RegExp(o,s.sensitive?"":"i");function a(p){const f=p.match(l),u={};if(!f)return null;for(let d=1;d<f.length;d++){const g=f[d]||"",k=r[d-1];u[k.name]=g&&k.repeatable?g.split("/"):g}return u}function c(p){let f="",u=!1;for(const d of t){(!u||!f.endsWith("/"))&&(f+="/"),u=!1;for(const g of d)if(g.type===0)f+=g.value;else if(g.type===1){const{value:k,repeatable:T,optional:N}=g,B=k in p?p[k]:"";if(Ut(B)&&!T)throw new Error(`Provided param "${k}" is an array but it is not repeatable (* or + modifiers)`);const C=Ut(B)?B.join("/"):B;if(!C)if(N)d.length<2&&(f.endsWith("/")?f=f.slice(0,-1):u=!0);else throw new Error(`Missing required param "${k}"`);f+=C}}return f||"/"}return{re:l,score:n,keys:r,parse:a,stringify:c}}function fu(t,e){let s=0;for(;s<t.length&&s<e.length;){const n=e[s]-t[s];if(n)return n;s++}return t.length<e.length?t.length===1&&t[0]===80?-1:1:t.length>e.length?e.length===1&&e[0]===80?1:-1:0}function Br(t,e){let s=0;const n=t.score,o=e.score;for(;s<n.length&&s<o.length;){const r=fu(n[s],o[s]);if(r)return r;s++}if(Math.abs(o.length-n.length)===1){if(Xo(n))return 1;if(Xo(o))return-1}return o.length-n.length}function Xo(t){const e=t[t.length-1];return t.length>0&&e[e.length-1]<0}const pu={type:0,value:""},mu=/[a-zA-Z0-9_]/;function hu(t){if(!t)return[[]];if(t==="/")return[[pu]];if(!t.startsWith("/"))throw new Error(`Invalid path "${t}"`);function e(g){throw new Error(`ERR (${s})/"${p}": ${g}`)}let s=0,n=s;const o=[];let r;function l(){r&&o.push(r),r=[]}let a=0,c,p="",f="";function u(){p&&(s===0?r.push({type:0,value:p}):s===1||s===2||s===3?(r.length>1&&(c==="*"||c==="+")&&e(`A repeatable param (${p}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:p,regexp:f,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):e("Invalid state to consume buffer"),p="")}function d(){p+=c}for(;a<t.length;){if(c=t[a++],c==="\\"&&s!==2){n=s,s=4;continue}switch(s){case 0:c==="/"?(p&&u(),l()):c===":"?(u(),s=1):d();break;case 4:d(),s=n;break;case 1:c==="("?s=2:mu.test(c)?d():(u(),s=0,c!=="*"&&c!=="?"&&c!=="+"&&a--);break;case 2:c===")"?f[f.length-1]=="\\"?f=f.slice(0,-1)+c:s=3:f+=c;break;case 3:u(),s=0,c!=="*"&&c!=="?"&&c!=="+"&&a--,f="";break;default:e("Unknown state");break}}return s===2&&e(`Unfinished custom RegExp for param "${p}"`),u(),l(),o}function gu(t,e,s){const n=du(hu(t.path),s),o=lt(n,{record:t,parent:e,children:[],alias:[]});return e&&!o.record.aliasOf==!e.record.aliasOf&&e.children.push(o),o}function xu(t,e){const s=[],n=new Map;e=si({strict:!1,end:!0,sensitive:!1},e);function o(u){return n.get(u)}function r(u,d,g){const k=!g,T=ti(u);T.aliasOf=g&&g.record;const N=si(e,u),B=[T];if("alias"in u){const M=typeof u.alias=="string"?[u.alias]:u.alias;for(const V of M)B.push(ti(lt({},T,{components:g?g.record.components:T.components,path:V,aliasOf:g?g.record:T})))}let C,P;for(const M of B){const{path:V}=M;if(d&&V[0]!=="/"){const Z=d.record.path,et=Z[Z.length-1]==="/"?"":"/";M.path=d.record.path+(V&&et+V)}if(C=gu(M,d,N),g?g.alias.push(C):(P=P||C,P!==C&&P.alias.push(C),k&&u.name&&!ei(C)&&l(u.name)),Nr(C)&&c(C),T.children){const Z=T.children;for(let et=0;et<Z.length;et++)r(Z[et],C,g&&g.children[et])}g=g||C}return P?()=>{l(P)}:ds}function l(u){if(Fr(u)){const d=n.get(u);d&&(n.delete(u),s.splice(s.indexOf(d),1),d.children.forEach(l),d.alias.forEach(l))}else{const d=s.indexOf(u);d>-1&&(s.splice(d,1),u.record.name&&n.delete(u.record.name),u.children.forEach(l),u.alias.forEach(l))}}function a(){return s}function c(u){const d=bu(u,s);s.splice(d,0,u),u.record.name&&!ei(u)&&n.set(u.record.name,u)}function p(u,d){let g,k={},T,N;if("name"in u&&u.name){if(g=n.get(u.name),!g)throw Ye(1,{location:u});N=g.record.name,k=lt(Zo(d.params,g.keys.filter(P=>!P.optional).concat(g.parent?g.parent.keys.filter(P=>P.optional):[]).map(P=>P.name)),u.params&&Zo(u.params,g.keys.map(P=>P.name))),T=g.stringify(k)}else if(u.path!=null)T=u.path,g=s.find(P=>P.re.test(T)),g&&(k=g.parse(T),N=g.record.name);else{if(g=d.name?n.get(d.name):s.find(P=>P.re.test(d.path)),!g)throw Ye(1,{location:u,currentLocation:d});N=g.record.name,k=lt({},d.params,u.params),T=g.stringify(k)}const B=[];let C=g;for(;C;)B.unshift(C.record),C=C.parent;return{name:N,path:T,params:k,matched:B,meta:vu(B)}}t.forEach(u=>r(u));function f(){s.length=0,n.clear()}return{addRoute:r,resolve:p,removeRoute:l,clearRoutes:f,getRoutes:a,getRecordMatcher:o}}function Zo(t,e){const s={};for(const n of e)n in t&&(s[n]=t[n]);return s}function ti(t){const e={path:t.path,redirect:t.redirect,name:t.name,meta:t.meta||{},aliasOf:t.aliasOf,beforeEnter:t.beforeEnter,props:yu(t),children:t.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in t?t.components||null:t.component&&{default:t.component}};return Object.defineProperty(e,"mods",{value:{}}),e}function yu(t){const e={},s=t.props||!1;if("component"in t)e.default=s;else for(const n in t.components)e[n]=typeof s=="object"?s[n]:s;return e}function ei(t){for(;t;){if(t.record.aliasOf)return!0;t=t.parent}return!1}function vu(t){return t.reduce((e,s)=>lt(e,s.meta),{})}function si(t,e){const s={};for(const n in t)s[n]=n in e?e[n]:t[n];return s}function bu(t,e){let s=0,n=e.length;for(;s!==n;){const r=s+n>>1;Br(t,e[r])<0?n=r:s=r+1}const o=_u(t);return o&&(n=e.lastIndexOf(o,n-1)),n}function _u(t){let e=t;for(;e=e.parent;)if(Nr(e)&&Br(t,e)===0)return e}function Nr({record:t}){return!!(t.name||t.components&&Object.keys(t.components).length||t.redirect)}function wu(t){const e={};if(t===""||t==="?")return e;const n=(t[0]==="?"?t.slice(1):t).split("&");for(let o=0;o<n.length;++o){const r=n[o].replace(Rr," "),l=r.indexOf("="),a=bs(l<0?r:r.slice(0,l)),c=l<0?null:bs(r.slice(l+1));if(a in e){let p=e[a];Ut(p)||(p=e[a]=[p]),p.push(c)}else e[a]=c}return e}function ni(t){let e="";for(let s in t){const n=t[s];if(s=zc(s),n==null){n!==void 0&&(e+=(e.length?"&":"")+s);continue}(Ut(n)?n.map(r=>r&&Ln(r)):[n&&Ln(n)]).forEach(r=>{r!==void 0&&(e+=(e.length?"&":"")+s,r!=null&&(e+="="+r))})}return e}function ku(t){const e={};for(const s in t){const n=t[s];n!==void 0&&(e[s]=Ut(n)?n.map(o=>o==null?null:""+o):n==null?n:""+n)}return e}const Su=Symbol(""),oi=Symbol(""),io=Symbol(""),Hr=Symbol(""),In=Symbol("");function Ze(){let t=[];function e(n){return t.push(n),()=>{const o=t.indexOf(n);o>-1&&t.splice(o,1)}}function s(){t=[]}return{add:e,list:()=>t.slice(),reset:s}}function _e(t,e,s,n,o,r=l=>l()){const l=n&&(n.enterCallbacks[o]=n.enterCallbacks[o]||[]);return()=>new Promise((a,c)=>{const p=d=>{d===!1?c(Ye(4,{from:s,to:e})):d instanceof Error?c(d):au(d)?c(Ye(2,{from:e,to:d})):(l&&n.enterCallbacks[o]===l&&typeof d=="function"&&l.push(d),a())},f=r(()=>t.call(n&&n.instances[o],e,s,p));let u=Promise.resolve(f);t.length<3&&(u=u.then(p)),u.catch(d=>c(d))})}function vn(t,e,s,n,o=r=>r()){const r=[];for(const l of t)for(const a in l.components){let c=l.components[a];if(!(e!=="beforeRouteEnter"&&!l.instances[a]))if(Er(c)){const f=(c.__vccOpts||c)[e];f&&r.push(_e(f,s,n,l,a,o))}else{let p=c();r.push(()=>p.then(f=>{if(!f)throw new Error(`Couldn't resolve component "${a}" at "${l.path}"`);const u=Rc(f)?f.default:f;l.mods[a]=f,l.components[a]=u;const g=(u.__vccOpts||u)[e];return g&&_e(g,s,n,l,a,o)()}))}}return r}function ii(t){const e=se(io),s=se(Hr),n=xt(()=>{const c=x(t.to);return e.resolve(c)}),o=xt(()=>{const{matched:c}=n.value,{length:p}=c,f=c[p-1],u=s.matched;if(!f||!u.length)return-1;const d=u.findIndex(Ge.bind(null,f));if(d>-1)return d;const g=ri(c[p-2]);return p>1&&ri(f)===g&&u[u.length-1].path!==g?u.findIndex(Ge.bind(null,c[p-2])):d}),r=xt(()=>o.value>-1&&Eu(s.params,n.value.params)),l=xt(()=>o.value>-1&&o.value===s.matched.length-1&&jr(s.params,n.value.params));function a(c={}){if(Tu(c)){const p=e[x(t.replace)?"replace":"push"](x(t.to)).catch(ds);return t.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>p),p}return Promise.resolve()}return{route:n,href:xt(()=>n.value.href),isActive:r,isExactActive:l,navigate:a}}function Cu(t){return t.length===1?t[0]:t}const $u=Ot({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:ii,setup(t,{slots:e}){const s=ws(ii(t)),{options:n}=se(io),o=xt(()=>({[li(t.activeClass,n.linkActiveClass,"router-link-active")]:s.isActive,[li(t.exactActiveClass,n.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const r=e.default&&Cu(e.default(s));return t.custom?r:vr("a",{"aria-current":s.isExactActive?t.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:o.value},r)}}}),Pu=$u;function Tu(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)&&!t.defaultPrevented&&!(t.button!==void 0&&t.button!==0)){if(t.currentTarget&&t.currentTarget.getAttribute){const e=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return t.preventDefault&&t.preventDefault(),!0}}function Eu(t,e){for(const s in e){const n=e[s],o=t[s];if(typeof n=="string"){if(n!==o)return!1}else if(!Ut(o)||o.length!==n.length||n.some((r,l)=>r!==o[l]))return!1}return!0}function ri(t){return t?t.aliasOf?t.aliasOf.path:t.path:""}const li=(t,e,s)=>t??e??s,Mu=Ot({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(t,{attrs:e,slots:s}){const n=se(In),o=xt(()=>t.route||n.value),r=se(oi,0),l=xt(()=>{let p=x(r);const{matched:f}=o.value;let u;for(;(u=f[p])&&!u.components;)p++;return p}),a=xt(()=>o.value.matched[l.value]);Ds(oi,xt(()=>l.value+1)),Ds(Su,a),Ds(In,o);const c=Ct();return as(()=>[c.value,a.value,t.name],([p,f,u],[d,g,k])=>{f&&(f.instances[u]=p,g&&g!==f&&p&&p===d&&(f.leaveGuards.size||(f.leaveGuards=g.leaveGuards),f.updateGuards.size||(f.updateGuards=g.updateGuards))),p&&f&&(!g||!Ge(f,g)||!d)&&(f.enterCallbacks[u]||[]).forEach(T=>T(p))},{flush:"post"}),()=>{const p=o.value,f=t.name,u=a.value,d=u&&u.components[f];if(!d)return ai(s.default,{Component:d,route:p});const g=u.props[f],k=g?g===!0?p.params:typeof g=="function"?g(p):g:null,N=vr(d,lt({},k,e,{onVnodeUnmounted:B=>{B.component.isUnmounted&&(u.instances[f]=null)},ref:c}));return ai(s.default,{Component:N,route:p})||N}}});function ai(t,e){if(!t)return null;const s=t(e);return s.length===1?s[0]:s}const zr=Mu;function Ru(t){const e=xu(t.routes,t),s=t.parseQuery||wu,n=t.stringifyQuery||ni,o=t.history,r=Ze(),l=Ze(),a=Ze(),c=wl(ge);let p=ge;ze&&t.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const f=xn.bind(null,_=>""+_),u=xn.bind(null,Vc),d=xn.bind(null,bs);function g(_,H){let j,z;return Fr(_)?(j=e.getRecordMatcher(_),z=H):z=_,e.addRoute(z,j)}function k(_){const H=e.getRecordMatcher(_);H&&e.removeRoute(H)}function T(){return e.getRoutes().map(_=>_.record)}function N(_){return!!e.getRecordMatcher(_)}function B(_,H){if(H=lt({},H||c.value),typeof _=="string"){const y=yn(s,_,H.path),v=e.resolve({path:y.path},H),S=o.createHref(y.fullPath);return lt(y,v,{params:d(v.params),hash:bs(y.hash),redirectedFrom:void 0,href:S})}let j;if(_.path!=null)j=lt({},_,{path:yn(s,_.path,H.path).path});else{const y=lt({},_.params);for(const v in y)y[v]==null&&delete y[v];j=lt({},_,{params:u(y)}),H.params=u(H.params)}const z=e.resolve(j,H),dt=_.hash||"";z.params=f(d(z.params));const m=qc(n,lt({},_,{hash:Hc(dt),path:z.path})),h=o.createHref(m);return lt({fullPath:m,hash:dt,query:n===ni?ku(_.query):_.query||{}},z,{redirectedFrom:void 0,href:h})}function C(_){return typeof _=="string"?yn(s,_,c.value.path):lt({},_)}function P(_,H){if(p!==_)return Ye(8,{from:H,to:_})}function M(_){return et(_)}function V(_){return M(lt(C(_),{replace:!0}))}function Z(_){const H=_.matched[_.matched.length-1];if(H&&H.redirect){const{redirect:j}=H;let z=typeof j=="function"?j(_):j;return typeof z=="string"&&(z=z.includes("?")||z.includes("#")?z=C(z):{path:z},z.params={}),lt({query:_.query,hash:_.hash,params:z.path!=null?{}:_.params},z)}}function et(_,H){const j=p=B(_),z=c.value,dt=_.state,m=_.force,h=_.replace===!0,y=Z(j);if(y)return et(lt(C(y),{state:typeof y=="object"?lt({},dt,y.state):dt,force:m,replace:h}),H||j);const v=j;v.redirectedFrom=H;let S;return!m&&Gc(n,z,j)&&(S=Ye(16,{to:v,from:z}),qt(z,z,!0,!1)),(S?Promise.resolve(S):q(v,z)).catch(w=>re(w)?re(w,2)?w:me(w):rt(w,v,z)).then(w=>{if(w){if(re(w,2))return et(lt({replace:h},C(w.to),{state:typeof w.to=="object"?lt({},dt,w.to.state):dt,force:m}),H||v)}else w=A(v,z,!0,h,dt);return tt(v,z,w),w})}function G(_,H){const j=P(_,H);return j?Promise.reject(j):Promise.resolve()}function D(_){const H=Le.values().next().value;return H&&typeof H.runWithContext=="function"?H.runWithContext(_):_()}function q(_,H){let j;const[z,dt,m]=Au(_,H);j=vn(z.reverse(),"beforeRouteLeave",_,H);for(const y of z)y.leaveGuards.forEach(v=>{j.push(_e(v,_,H))});const h=G.bind(null,_,H);return j.push(h),Nt(j).then(()=>{j=[];for(const y of r.list())j.push(_e(y,_,H));return j.push(h),Nt(j)}).then(()=>{j=vn(dt,"beforeRouteUpdate",_,H);for(const y of dt)y.updateGuards.forEach(v=>{j.push(_e(v,_,H))});return j.push(h),Nt(j)}).then(()=>{j=[];for(const y of m)if(y.beforeEnter)if(Ut(y.beforeEnter))for(const v of y.beforeEnter)j.push(_e(v,_,H));else j.push(_e(y.beforeEnter,_,H));return j.push(h),Nt(j)}).then(()=>(_.matched.forEach(y=>y.enterCallbacks={}),j=vn(m,"beforeRouteEnter",_,H,D),j.push(h),Nt(j))).then(()=>{j=[];for(const y of l.list())j.push(_e(y,_,H));return j.push(h),Nt(j)}).catch(y=>re(y,8)?y:Promise.reject(y))}function tt(_,H,j){a.list().forEach(z=>D(()=>z(_,H,j)))}function A(_,H,j,z,dt){const m=P(_,H);if(m)return m;const h=H===ge,y=ze?history.state:{};j&&(z||h?o.replace(_.fullPath,lt({scroll:h&&y&&y.scroll},dt)):o.push(_.fullPath,dt)),c.value=_,qt(_,H,j,h),me()}let X;function ft(){X||(X=o.listen((_,H,j)=>{if(!Ps.listening)return;const z=B(_),dt=Z(z);if(dt){et(lt(dt,{replace:!0,force:!0}),z).catch(ds);return}p=z;const m=c.value;ze&&su(Go(m.fullPath,j.delta),nn()),q(z,m).catch(h=>re(h,12)?h:re(h,2)?(et(lt(C(h.to),{force:!0}),z).then(y=>{re(y,20)&&!j.delta&&j.type===_s.pop&&o.go(-1,!1)}).catch(ds),Promise.reject()):(j.delta&&o.go(-j.delta,!1),rt(h,z,m))).then(h=>{h=h||A(z,m,!1),h&&(j.delta&&!re(h,8)?o.go(-j.delta,!1):j.type===_s.pop&&re(h,20)&&o.go(-1,!1)),tt(z,m,h)}).catch(ds)}))}let _t=Ze(),ct=Ze(),Q;function rt(_,H,j){me(_);const z=ct.list();return z.length?z.forEach(dt=>dt(_,H,j)):console.error(_),Promise.reject(_)}function oe(){return Q&&c.value!==ge?Promise.resolve():new Promise((_,H)=>{_t.add([_,H])})}function me(_){return Q||(Q=!_,ft(),_t.list().forEach(([H,j])=>_?j(_):H()),_t.reset()),_}function qt(_,H,j,z){const{scrollBehavior:dt}=t;if(!ze||!dt)return Promise.resolve();const m=!j&&nu(Go(_.fullPath,0))||(z||!j)&&history.state&&history.state.scroll||null;return Qn().then(()=>dt(_,H,m)).then(h=>h&&eu(h)).catch(h=>rt(h,_,H))}const Mt=_=>o.go(_);let je;const Le=new Set,Ps={currentRoute:c,listening:!0,addRoute:g,removeRoute:k,clearRoutes:e.clearRoutes,hasRoute:N,getRoutes:T,resolve:B,options:t,push:M,replace:V,go:Mt,back:()=>Mt(-1),forward:()=>Mt(1),beforeEach:r.add,beforeResolve:l.add,afterEach:a.add,onError:ct.add,isReady:oe,install(_){const H=this;_.component("RouterLink",Pu),_.component("RouterView",zr),_.config.globalProperties.$router=H,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>x(c)}),ze&&!je&&c.value===ge&&(je=!0,M(o.location).catch(dt=>{}));const j={};for(const dt in ge)Object.defineProperty(j,dt,{get:()=>c.value[dt],enumerable:!0});_.provide(io,H),_.provide(Hr,ji(j)),_.provide(In,c);const z=_.unmount;Le.add(_),_.unmount=function(){Le.delete(_),Le.size<1&&(p=ge,X&&X(),X=null,c.value=ge,je=!1,Q=!1),z()}}};function Nt(_){return _.reduce((H,j)=>H.then(()=>D(j)),Promise.resolve())}return Ps}function Au(t,e){const s=[],n=[],o=[],r=Math.max(e.matched.length,t.matched.length);for(let l=0;l<r;l++){const a=e.matched[l];a&&(t.matched.find(p=>Ge(p,a))?n.push(a):s.push(a));const c=t.matched[l];c&&(e.matched.find(p=>Ge(p,c))||o.push(c))}return[s,n,o]}const Cs=no("ui",()=>{const t=Ct(!1),e=Ct(!1),s=Ct("dimensions"),n=Ct(!1),o=Ct([]),r=()=>{t.value=!0},l=()=>{t.value=!1},a=()=>{e.value=!0},c=()=>{e.value=!1},p=C=>{s.value=C},f=C=>{n.value=C},u=C=>{const P=Date.now().toString()+Math.random().toString(36).substr(2,9),M={id:P,duration:5e3,persistent:!1,...C};o.value.push(M),!M.persistent&&M.duration&&setTimeout(()=>{d(P)},M.duration)},d=C=>{const P=o.value.findIndex(M=>M.id===C);P>-1&&o.value.splice(P,1)};return{pricingModalOpen:t,reportModalOpen:e,activeTab:s,loading:n,notifications:o,openPricingModal:r,closePricingModal:l,openReportModal:a,closeReportModal:c,setActiveTab:p,setLoading:f,addNotification:u,removeNotification:d,clearAllNotifications:()=>{o.value=[]},showSuccess:(C,P)=>{u({type:"success",title:C,message:P})},showError:(C,P)=>{u({type:"error",title:C,message:P,duration:8e3})},showWarning:(C,P)=>{u({type:"warning",title:C,message:P,duration:6e3})},showInfo:(C,P)=>{u({type:"info",title:C,message:P})}}}),As={8:.395,10:.617,12:.888,14:1.208,16:1.578,18:2,20:2.466,22:2.984,25:3.853},ne=no("wallCalculation",()=>{const t=Ct({length:10,height:2,thickness:20,foundationDepth:80,foundationWidth:40}),e=Ct({foundation:{longitudinalDiameter:12,longitudinalCount:4,stirrupDiameter:8,stirrupSpacing:20},wall:{verticalDiameter:12,verticalSpacing:20,horizontalDiameter:10,horizontalSpacing:25},general:{concreteCover:3,foundationWallExtension:40}}),s=Ct({coping:!0,plaster:{enabled:!0,doubleSided:!1},paint:{enabled:!0,doubleSided:!1}}),n=xt(()=>{const{length:c,height:p,thickness:f,foundationDepth:u,foundationWidth:d}=t.value,g=f/100,k=u/100,T=d/100,N=c*p*g,B=c*k*T,C=s.value.coping?c*.1*g:0,P=N+B+C;let M=0;if(s.value.plaster.enabled){const Z=c*p;M=s.value.plaster.doubleSided?Z*2:Z}let V=0;if(s.value.paint.enabled){const Z=c*p;V=s.value.paint.doubleSided?Z*2:Z}return{wallConcrete:N,foundationConcrete:B,copingConcrete:C,totalConcrete:P,plasterArea:M,paintArea:V}}),o=xt(()=>{const{length:c,height:p,foundationDepth:f,foundationWidth:u}=t.value,{foundation:d,wall:g,general:k}=e.value,T=f/100,N=u/100,B=k.concreteCover/100,C=k.foundationWallExtension/100,P=c*d.longitudinalCount,M=2*(N-2*B+(T-2*B)),V=Math.ceil(c/(d.stirrupSpacing/100)),Z=M*V,G=Math.ceil(c/(g.verticalSpacing/100))*(p+C),q=Math.ceil(p/(g.horizontalSpacing/100))*c,tt=P*As[d.longitudinalDiameter],A=Z*As[d.stirrupDiameter],X=G*As[g.verticalDiameter],ft=q*As[g.horizontalDiameter],_t=tt+A+X+ft,ct=_t/1e3;return{foundation:{longitudinalLength:P,stirrupLength:Z,longitudinalWeight:tt,stirrupWeight:A},wall:{verticalLength:G,horizontalLength:q,verticalWeight:X,horizontalWeight:ft},totalWeight:_t,totalWeightTons:ct}});return{dimensions:t,rebarDetails:e,surfaceOptions:s,volumeCalculations:n,rebarCalculations:o,updateDimensions:c=>{t.value={...t.value,...c}},updateRebarDetails:c=>{e.value={...e.value,...c}},updateSurfaceOptions:c=>{s.value={...s.value,...c}}}}),$s=no("pricing",()=>{const t=Ct({readyMixConcrete:800,rebar:25e3,plasterMortar:45,exteriorPaint:35,coping:120}),e=Ct({excavation:25,formwork:85,concretePouring:120,rebarInstallation:3500,plastering:65,painting:25}),s=Ct({wastagePercentage:8,vatPercentage:20,profitMargin:15}),n=xt(()=>{const f=ne(),{volumeCalculations:u,rebarCalculations:d,dimensions:g}=f,k=1+s.value.wastagePercentage/100,T=u.totalConcrete*k*t.value.readyMixConcrete,N=d.totalWeightTons*k*t.value.rebar,B=u.plasterArea*k*t.value.plasterMortar,C=u.paintArea*k*t.value.exteriorPaint,P=f.surfaceOptions.coping?g.length*k*t.value.coping:0,M=T+N+B+C+P,V=u.foundationConcrete*e.value.excavation,Z=2*g.length*(g.foundationDepth/100),et=2*g.length*g.height,D=(Z+et)*e.value.formwork,q=u.totalConcrete*e.value.concretePouring,tt=d.totalWeightTons*e.value.rebarInstallation,A=u.plasterArea*e.value.plastering,X=u.paintArea*e.value.painting,ft=V+D+q+tt+A+X,_t=M+ft;return{materials:{concrete:T,rebar:N,plaster:B,paint:C,coping:P,total:M},labor:{excavation:V,formwork:D,concretePouring:q,rebarInstallation:tt,plastering:A,painting:X,total:ft},grandTotal:_t}}),o=xt(()=>{const f=n.value.grandTotal,u=f*(1+s.value.profitMargin/100),d=u*(1+s.value.vatPercentage/100);return{baseTotal:f,profitAmount:u-f,totalWithProfit:u,vatAmount:d-u,finalTotal:d}}),r=xt(()=>{const f=ne(),u=f.dimensions.length*f.dimensions.height,d=f.volumeCalculations.totalConcrete;return{perSquareMeter:o.value.finalTotal/u,perCubicMeter:o.value.finalTotal/d,perLinearMeter:o.value.finalTotal/f.dimensions.length}});return{materialPrices:t,laborPrices:e,projectSettings:s,costBreakdown:n,finalPricing:o,unitCosts:r,updateMaterialPrices:f=>{t.value={...t.value,...f}},updateLaborPrices:f=>{e.value={...e.value,...f}},updateProjectSettings:f=>{s.value={...s.value,...f}},resetToDefaults:()=>{t.value={readyMixConcrete:800,rebar:25e3,plasterMortar:45,exteriorPaint:35,coping:120},e.value={excavation:25,formwork:85,concretePouring:120,rebarInstallation:3500,plastering:65,painting:25},s.value={wastagePercentage:8,vatPercentage:20,profitMargin:15}}}}),Du={class:"space-y-6"},Ou={class:"mb-4"},ju=["value"],Lu={class:"mb-4"},Fu=["value"],Iu={class:"mb-4"},Bu=["value"],Nu={class:"mb-4"},Hu=["value"],zu={class:"mb-4"},Wu=["value"],Vu={class:"bg-gray-50 rounded-lg p-4"},Ku={class:"grid grid-cols-2 gap-3 text-sm"},Uu={class:"font-medium ml-2"},qu={class:"font-medium ml-2"},Gu={class:"font-medium ml-2"},Yu={class:"font-medium ml-2 text-primary-600"},Ju=Ot({__name:"DimensionsPanel",setup(t){const e=ne(),s=(n,o)=>{e.updateDimensions({[n]:o})};return(n,o)=>(F(),I("div",Du,[i("div",null,[o[8]||(o[8]=i("h3",{class:"section-title"},"Duvar Boyutları",-1)),i("div",Ou,[o[5]||(o[5]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Duvar Uzunluğu (m) ",-1)),i("input",{type:"number",value:x(e).dimensions.length,onInput:o[0]||(o[0]=r=>s("length",Number(r.target.value))),min:"1",max:"100",step:"0.1",class:"input-field"},null,40,ju)]),i("div",Lu,[o[6]||(o[6]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Duvar Yüksekliği (m) ",-1)),i("input",{type:"number",value:x(e).dimensions.height,onInput:o[1]||(o[1]=r=>s("height",Number(r.target.value))),min:"0.5",max:"10",step:"0.1",class:"input-field"},null,40,Fu)]),i("div",Iu,[o[7]||(o[7]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Duvar Kalınlığı (cm) ",-1)),i("input",{type:"number",value:x(e).dimensions.thickness,onInput:o[2]||(o[2]=r=>s("thickness",Number(r.target.value))),min:"10",max:"50",step:"1",class:"input-field"},null,40,Bu)])]),i("div",null,[o[11]||(o[11]=i("h3",{class:"section-title"},"Temel Boyutları",-1)),i("div",Nu,[o[9]||(o[9]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Temel Derinliği (cm) ",-1)),i("input",{type:"number",value:x(e).dimensions.foundationDepth,onInput:o[3]||(o[3]=r=>s("foundationDepth",Number(r.target.value))),min:"30",max:"200",step:"5",class:"input-field"},null,40,Hu)]),i("div",zu,[o[10]||(o[10]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Temel Genişliği (cm) ",-1)),i("input",{type:"number",value:x(e).dimensions.foundationWidth,onInput:o[4]||(o[4]=r=>s("foundationWidth",Number(r.target.value))),min:"20",max:"100",step:"5",class:"input-field"},null,40,Wu)])]),i("div",Vu,[o[16]||(o[16]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Hesaplanan Değerler",-1)),i("div",Ku,[i("div",null,[o[12]||(o[12]=i("span",{class:"text-gray-500"},"Duvar Alanı:",-1)),i("span",Uu,b((x(e).dimensions.length*x(e).dimensions.height).toFixed(2))+" m²",1)]),i("div",null,[o[13]||(o[13]=i("span",{class:"text-gray-500"},"Duvar Hacmi:",-1)),i("span",qu,b(x(e).volumeCalculations.wallConcrete.toFixed(2))+" m³",1)]),i("div",null,[o[14]||(o[14]=i("span",{class:"text-gray-500"},"Temel Hacmi:",-1)),i("span",Gu,b(x(e).volumeCalculations.foundationConcrete.toFixed(2))+" m³",1)]),i("div",null,[o[15]||(o[15]=i("span",{class:"text-gray-500"},"Toplam Beton:",-1)),i("span",Yu,b(x(e).volumeCalculations.totalConcrete.toFixed(2))+" m³",1)])])])]))}}),Qu={class:"space-y-6"},Xu={class:"space-y-4"},Zu=["value"],td=["value"],ed=["value"],sd=["value"],nd=["value"],od=["value"],id={class:"space-y-4"},rd=["value"],ld=["value"],ad=["value"],cd=["value"],ud=["value"],dd=["value"],fd={class:"space-y-4"},pd=["value"],md=["value"],hd={class:"bg-gray-50 rounded-lg p-4"},gd={class:"space-y-2 text-sm"},xd={class:"flex justify-between"},yd={class:"font-medium text-primary-600"},vd={class:"flex justify-between"},bd={class:"font-medium text-primary-600"},_d=Ot({__name:"RebarPanel",setup(t){const e=ne(),s=[8,10,12,14,16,18,20,22,25],n=(l,a)=>{e.updateRebarDetails({foundation:{...e.rebarDetails.foundation,[l]:a}})},o=(l,a)=>{e.updateRebarDetails({wall:{...e.rebarDetails.wall,[l]:a}})},r=(l,a)=>{e.updateRebarDetails({general:{...e.rebarDetails.general,[l]:a}})};return(l,a)=>(F(),I("div",Qu,[i("div",null,[a[14]||(a[14]=i("h3",{class:"section-title"},"Temel Donatısı",-1)),i("div",Xu,[i("div",null,[a[10]||(a[10]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Boyuna Demir Çapı ",-1)),i("select",{value:x(e).rebarDetails.foundation.longitudinalDiameter,onChange:a[0]||(a[0]=c=>n("longitudinalDiameter",Number(c.target.value))),class:"input-field"},[(F(),I(gt,null,Dt(s,c=>i("option",{key:c,value:c}," Ø"+b(c)+"mm ",9,td)),64))],40,Zu)]),i("div",null,[a[11]||(a[11]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Boyuna Demir Adedi ",-1)),i("input",{type:"number",value:x(e).rebarDetails.foundation.longitudinalCount,onInput:a[1]||(a[1]=c=>n("longitudinalCount",Number(c.target.value))),min:"2",max:"20",step:"1",class:"input-field"},null,40,ed)]),i("div",null,[a[12]||(a[12]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Etriye Çapı ",-1)),i("select",{value:x(e).rebarDetails.foundation.stirrupDiameter,onChange:a[2]||(a[2]=c=>n("stirrupDiameter",Number(c.target.value))),class:"input-field"},[(F(!0),I(gt,null,Dt(s.slice(0,4),c=>(F(),I("option",{key:c,value:c}," Ø"+b(c)+"mm ",9,nd))),128))],40,sd)]),i("div",null,[a[13]||(a[13]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Etriye Aralığı (cm) ",-1)),i("input",{type:"number",value:x(e).rebarDetails.foundation.stirrupSpacing,onInput:a[3]||(a[3]=c=>n("stirrupSpacing",Number(c.target.value))),min:"10",max:"50",step:"5",class:"input-field"},null,40,od)])])]),i("div",null,[a[19]||(a[19]=i("h3",{class:"section-title"},"Duvar Donatısı",-1)),i("div",id,[i("div",null,[a[15]||(a[15]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Dikey Demir Çapı ",-1)),i("select",{value:x(e).rebarDetails.wall.verticalDiameter,onChange:a[4]||(a[4]=c=>o("verticalDiameter",Number(c.target.value))),class:"input-field"},[(F(),I(gt,null,Dt(s,c=>i("option",{key:c,value:c}," Ø"+b(c)+"mm ",9,ld)),64))],40,rd)]),i("div",null,[a[16]||(a[16]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Dikey Demir Aralığı (cm) ",-1)),i("input",{type:"number",value:x(e).rebarDetails.wall.verticalSpacing,onInput:a[5]||(a[5]=c=>o("verticalSpacing",Number(c.target.value))),min:"10",max:"50",step:"5",class:"input-field"},null,40,ad)]),i("div",null,[a[17]||(a[17]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Yatay Demir Çapı ",-1)),i("select",{value:x(e).rebarDetails.wall.horizontalDiameter,onChange:a[6]||(a[6]=c=>o("horizontalDiameter",Number(c.target.value))),class:"input-field"},[(F(),I(gt,null,Dt(s,c=>i("option",{key:c,value:c}," Ø"+b(c)+"mm ",9,ud)),64))],40,cd)]),i("div",null,[a[18]||(a[18]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Yatay Demir Aralığı (cm) ",-1)),i("input",{type:"number",value:x(e).rebarDetails.wall.horizontalSpacing,onInput:a[7]||(a[7]=c=>o("horizontalSpacing",Number(c.target.value))),min:"10",max:"50",step:"5",class:"input-field"},null,40,dd)])])]),i("div",null,[a[22]||(a[22]=i("h3",{class:"section-title"},"Genel Parametreler",-1)),i("div",fd,[i("div",null,[a[20]||(a[20]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Beton Paspayı (cm) ",-1)),i("input",{type:"number",value:x(e).rebarDetails.general.concreteCover,onInput:a[8]||(a[8]=c=>r("concreteCover",Number(c.target.value))),min:"2",max:"10",step:"0.5",class:"input-field"},null,40,pd)]),i("div",null,[a[21]||(a[21]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Temel-Duvar Filiz Payı (cm) ",-1)),i("input",{type:"number",value:x(e).rebarDetails.general.foundationWallExtension,onInput:a[9]||(a[9]=c=>r("foundationWallExtension",Number(c.target.value))),min:"20",max:"100",step:"5",class:"input-field"},null,40,md)])])]),i("div",hd,[a[25]||(a[25]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Donatı Özeti",-1)),i("div",gd,[i("div",xd,[a[23]||(a[23]=i("span",{class:"text-gray-500"},"Toplam Demir Ağırlığı:",-1)),i("span",yd,b(x(e).rebarCalculations.totalWeight.toFixed(0))+" kg",1)]),i("div",vd,[a[24]||(a[24]=i("span",{class:"text-gray-500"},"Toplam Demir (Ton):",-1)),i("span",bd,b(x(e).rebarCalculations.totalWeightTons.toFixed(2))+" ton",1)])])])]))}}),wd={class:"space-y-6"},kd={class:"flex items-center"},Sd=["checked"],Cd={class:"space-y-3"},$d={class:"flex items-center"},Pd=["checked"],Td={key:0,class:"ml-6"},Ed={class:"flex items-center"},Md=["checked"],Rd={class:"space-y-3"},Ad={class:"flex items-center"},Dd=["checked"],Od={key:0,class:"ml-6"},jd={class:"flex items-center"},Ld=["checked"],Fd={class:"bg-gray-50 rounded-lg p-4"},Id={class:"space-y-2 text-sm"},Bd={class:"flex justify-between"},Nd={class:"font-medium"},Hd={class:"flex justify-between"},zd={class:"font-medium"},Wd={class:"flex justify-between"},Vd={class:"font-medium"},Kd=Ot({__name:"SurfacePanel",setup(t){const e=ne(),s=(r,l)=>{e.updateSurfaceOptions({[r]:l})},n=(r,l)=>{e.updateSurfaceOptions({plaster:{...e.surfaceOptions.plaster,[r]:l}})},o=(r,l)=>{e.updateSurfaceOptions({paint:{...e.surfaceOptions.paint,[r]:l}})};return(r,l)=>(F(),I("div",wd,[i("div",null,[l[6]||(l[6]=i("h3",{class:"section-title"},"Harpuşta",-1)),i("div",kd,[i("input",{id:"coping",type:"checkbox",checked:x(e).surfaceOptions.coping,onChange:l[0]||(l[0]=a=>s("coping",a.target.checked)),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Sd),l[5]||(l[5]=i("label",{for:"coping",class:"ml-2 block text-sm text-gray-900"}," Duvar üstü harpuşta yapılsın ",-1))]),l[7]||(l[7]=i("p",{class:"mt-2 text-xs text-gray-500"}," Harpuşta, duvarın üst kısmına yapılan koruyucu beton katmanıdır (10cm yükseklik). ",-1))]),i("div",null,[l[11]||(l[11]=i("h3",{class:"section-title"},"Sıva",-1)),i("div",Cd,[i("div",$d,[i("input",{id:"plaster",type:"checkbox",checked:x(e).surfaceOptions.plaster.enabled,onChange:l[1]||(l[1]=a=>n("enabled",a.target.checked)),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Pd),l[8]||(l[8]=i("label",{for:"plaster",class:"ml-2 block text-sm text-gray-900"}," Sıva yapılsın ",-1))]),x(e).surfaceOptions.plaster.enabled?(F(),I("div",Td,[i("div",Ed,[i("input",{id:"plaster-double",type:"checkbox",checked:x(e).surfaceOptions.plaster.doubleSided,onChange:l[2]||(l[2]=a=>n("doubleSided",a.target.checked)),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Md),l[9]||(l[9]=i("label",{for:"plaster-double",class:"ml-2 block text-sm text-gray-900"}," Çift yüzey sıva ",-1))]),l[10]||(l[10]=i("p",{class:"mt-1 text-xs text-gray-500"}," İşaretlenirse duvarın her iki yüzeyine sıva yapılır. ",-1))])):ot("",!0)])]),i("div",null,[l[15]||(l[15]=i("h3",{class:"section-title"},"Boya",-1)),i("div",Rd,[i("div",Ad,[i("input",{id:"paint",type:"checkbox",checked:x(e).surfaceOptions.paint.enabled,onChange:l[3]||(l[3]=a=>o("enabled",a.target.checked)),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Dd),l[12]||(l[12]=i("label",{for:"paint",class:"ml-2 block text-sm text-gray-900"}," Boya yapılsın ",-1))]),x(e).surfaceOptions.paint.enabled?(F(),I("div",Od,[i("div",jd,[i("input",{id:"paint-double",type:"checkbox",checked:x(e).surfaceOptions.paint.doubleSided,onChange:l[4]||(l[4]=a=>o("doubleSided",a.target.checked)),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Ld),l[13]||(l[13]=i("label",{for:"paint-double",class:"ml-2 block text-sm text-gray-900"}," Çift yüzey boya ",-1))]),l[14]||(l[14]=i("p",{class:"mt-1 text-xs text-gray-500"}," İşaretlenirse duvarın her iki yüzeyine boya yapılır. ",-1))])):ot("",!0)])]),i("div",Fd,[l[19]||(l[19]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Yüzey İşlemleri Özeti",-1)),i("div",Id,[i("div",Bd,[l[16]||(l[16]=i("span",{class:"text-gray-500"},"Harpuşta:",-1)),i("span",Nd,b(x(e).surfaceOptions.coping?"Evet":"Hayır"),1)]),i("div",Hd,[l[17]||(l[17]=i("span",{class:"text-gray-500"},"Sıva Alanı:",-1)),i("span",zd,b(x(e).volumeCalculations.plasterArea.toFixed(2))+" m² "+b(x(e).surfaceOptions.plaster.doubleSided?"(Çift Yüzey)":"(Tek Yüzey)"),1)]),i("div",Wd,[l[18]||(l[18]=i("span",{class:"text-gray-500"},"Boya Alanı:",-1)),i("span",Vd,b(x(e).volumeCalculations.paintArea.toFixed(2))+" m² "+b(x(e).surfaceOptions.paint.doubleSided?"(Çift Yüzey)":"(Tek Yüzey)"),1)])])])]))}}),Ud={class:"bg-white shadow-lg border-b border-gray-200 fixed top-0 left-0 right-0 z-40"},qd={class:"px-4 lg:px-6 py-4"},Gd={class:"flex items-center justify-between"},Yd={class:"hidden lg:flex items-center space-x-6"},Jd={class:"text-center"},Qd={class:"text-lg font-semibold text-gray-900"},Xd={class:"text-center"},Zd={class:"text-lg font-semibold text-gray-900"},tf={class:"text-center"},ef={class:"text-lg font-semibold text-primary-600"},sf={class:"text-center"},nf={class:"text-lg font-semibold text-primary-600"},of={class:"flex items-center space-x-3"},rf={class:"hidden md:flex items-center space-x-4"},lf={class:"flex items-center space-x-2"},af=["aria-expanded"],cf={class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},uf={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"},df={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},ff={key:0,class:"md:hidden border-t border-gray-200 bg-white"},pf={class:"px-4 py-3 space-y-2"},mf={class:"bg-gray-50 border-t border-gray-200"},hf={class:"px-4 lg:px-6"},gf={class:"flex space-x-0","aria-label":"Hesaplama Seçenekleri",role:"tablist"},xf=["onClick","aria-selected","aria-controls","tabindex"],yf={class:"flex flex-col lg:flex-row items-center lg:space-x-2 space-y-1 lg:space-y-0"},vf={class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true"},bf=["d"],_f={class:"text-xs lg:text-sm"},wf={class:"bg-white border-t border-gray-200 shadow-lg"},kf={class:"px-4 lg:px-6 py-4 max-h-80 overflow-y-auto"},Sf={key:0,id:"panel-dimensions",role:"tabpanel","aria-labelledby":"tab-dimensions"},Cf={key:1,id:"panel-rebar",role:"tabpanel","aria-labelledby":"tab-rebar"},$f={key:2,id:"panel-surface",role:"tabpanel","aria-labelledby":"tab-surface"},Pf=Ot({__name:"AppHeader",setup(t){const e=Cs(),s=$s(),n=ne(),o=Ct(!1),r=[{id:"dimensions",name:"Boyutlar",icon:"M4 6h16M4 12h16M4 18h16"},{id:"rebar",name:"Donatı",icon:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},{id:"surface",name:"Yüzey",icon:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z"}],l=f=>{e.setActiveTab(f)},a=()=>{e.openPricingModal()},c=()=>{e.openReportModal()},p=()=>{o.value=!o.value};return(f,u)=>{const d=Jl("router-link");return F(),I("header",Ud,[i("div",qd,[i("div",Gd,[u[10]||(u[10]=ce('<div class="flex items-center space-x-3"><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-blue-600 rounded-lg flex items-center justify-center"><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg></div><div><h1 class="text-xl font-bold text-gray-900">Perde Duvar Analiz</h1><p class="text-sm text-gray-500">Profesyonel Hesaplama Aracı</p></div></div>',1)),i("div",Yd,[i("div",Jd,[i("div",Qd,b(x(n).dimensions.length)+"m ",1),u[2]||(u[2]=i("div",{class:"text-xs text-gray-500"},"Uzunluk",-1))]),i("div",Xd,[i("div",Zd,b(x(n).dimensions.height)+"m ",1),u[3]||(u[3]=i("div",{class:"text-xs text-gray-500"},"Yükseklik",-1))]),i("div",tf,[i("div",ef,b(x(n).volumeCalculations.totalConcrete.toFixed(2))+"m³ ",1),u[4]||(u[4]=i("div",{class:"text-xs text-gray-500"},"Toplam Beton",-1))]),i("div",sf,[i("div",nf," ₺"+b(x(s).finalPricing.finalTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1),u[5]||(u[5]=i("div",{class:"text-xs text-gray-500"},"Toplam Maliyet",-1))])]),i("div",of,[i("nav",rf,[it(d,{to:"/",class:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors","active-class":"text-primary-600 bg-primary-50"},{default:We(()=>u[6]||(u[6]=[es(" Ana Sayfa ")])),_:1,__:[6]}),it(d,{to:"/about",class:"text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors","active-class":"text-primary-600 bg-primary-50"},{default:We(()=>u[7]||(u[7]=[es(" Hakkında ")])),_:1,__:[7]})]),i("div",lf,[i("button",{onClick:a,class:"flex items-center space-x-2 px-3 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors duration-200 text-sm",title:"Fiyat Ayarları"},u[8]||(u[8]=[i("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1),i("span",{class:"hidden lg:inline"},"Fiyatlar",-1)])),i("button",{onClick:c,class:"flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 text-sm",title:"Rapor Oluştur"},u[9]||(u[9]=[i("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),i("span",{class:"hidden lg:inline"},"Rapor",-1)])),i("button",{onClick:p,class:"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors","aria-expanded":o.value,"aria-label":"Ana menüyü aç"},[(F(),I("svg",cf,[o.value?(F(),I("path",df)):(F(),I("path",uf))]))],8,af)])])]),o.value?(F(),I("div",ff,[i("div",pf,[it(d,{to:"/",onClick:u[0]||(u[0]=g=>o.value=!1),class:"block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors","active-class":"text-primary-600 bg-primary-50"},{default:We(()=>u[11]||(u[11]=[es(" Ana Sayfa ")])),_:1,__:[11]}),it(d,{to:"/about",onClick:u[1]||(u[1]=g=>o.value=!1),class:"block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors","active-class":"text-primary-600 bg-primary-50"},{default:We(()=>u[12]||(u[12]=[es(" Hakkında ")])),_:1,__:[12]})])])):ot("",!0)]),i("div",mf,[i("div",hf,[i("nav",gf,[(F(),I(gt,null,Dt(r,g=>i("button",{key:g.id,onClick:k=>l(g.id),class:Ft(["flex-1 lg:flex-none lg:px-6 py-3 px-2 text-center text-sm font-medium transition-colors duration-200 border-b-2",x(e).activeTab===g.id?"border-primary-500 text-primary-600 bg-white":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-100"]),"aria-selected":x(e).activeTab===g.id,"aria-controls":`panel-${g.id}`,role:"tab",tabindex:x(e).activeTab===g.id?0:-1},[i("div",yf,[(F(),I("svg",vf,[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:g.icon},null,8,bf)])),i("span",_f,b(g.name),1)])],10,xf)),64))])])]),i("div",wf,[i("div",kf,[x(e).activeTab==="dimensions"?(F(),I("div",Sf,[it(Ju)])):ot("",!0),x(e).activeTab==="rebar"?(F(),I("div",Cf,[it(_d)])):ot("",!0),x(e).activeTab==="surface"?(F(),I("div",$f,[it(Kd)])):ot("",!0)])])])}}}),Tf={"aria-live":"assertive",class:"fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50"},Ef={class:"w-full flex flex-col items-center space-y-4 sm:items-end"},Mf={class:"p-4"},Rf={class:"flex items-start"},Af={class:"flex-shrink-0"},Df=["d"],Of={class:"ml-3 w-0 flex-1 pt-0.5"},jf={class:"text-sm font-medium text-gray-900"},Lf={class:"mt-1 text-sm text-gray-500"},Ff={class:"ml-4 flex-shrink-0 flex"},If=["onClick"],Bf=Ot({__name:"NotificationContainer",setup(t){const e=Cs(),s=l=>{switch(l){case"success":return"M5 13l4 4L19 7";case"error":return"M6 18L18 6M6 6l12 12";case"warning":return"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z";case"info":default:return"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"}},n=l=>{const a="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden";switch(l){case"success":return`${a} border-l-4 border-green-400`;case"error":return`${a} border-l-4 border-red-400`;case"warning":return`${a} border-l-4 border-yellow-400`;case"info":default:return`${a} border-l-4 border-blue-400`}},o=l=>{switch(l){case"success":return"text-green-400";case"error":return"text-red-400";case"warning":return"text-yellow-400";case"info":default:return"text-blue-400"}},r=l=>{e.removeNotification(l)};return(l,a)=>(F(),I("div",Tf,[i("div",Ef,[it(hc,{name:"notification",tag:"div",class:"w-full flex flex-col items-center space-y-4 sm:items-end"},{default:We(()=>[(F(!0),I(gt,null,Dt(x(e).notifications,c=>(F(),I("div",{key:c.id,class:Ft(n(c.type))},[i("div",Mf,[i("div",Rf,[i("div",Af,[(F(),I("svg",{class:Ft(["h-6 w-6",o(c.type)]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:s(c.type)},null,8,Df)],2))]),i("div",Of,[i("p",jf,b(c.title),1),i("p",Lf,b(c.message),1)]),i("div",Ff,[i("button",{onClick:p=>r(c.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},a[0]||(a[0]=[i("span",{class:"sr-only"},"Kapat",-1),i("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,If)])])])],2))),128))]),_:1})])]))}}),Nf=(t,e)=>{const s=t.__vccOpts||t;for(const[n,o]of e)s[n]=o;return s},Hf=Nf(Bf,[["__scopeId","data-v-d29e3185"]]),zf={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Wf={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Vf={class:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"},Kf={class:"bg-gray-50 px-6"},Uf={class:"flex space-x-8","aria-label":"Tabs"},qf={class:"bg-white px-6 py-6 max-h-96 overflow-y-auto"},Gf={key:0,class:"space-y-6"},Yf={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Jf=["value"],Qf=["value"],Xf=["value"],Zf=["value"],tp=["value"],ep={key:1,class:"space-y-6"},sp={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},np=["value"],op=["value"],ip=["value"],rp=["value"],lp=["value"],ap=["value"],cp={key:2,class:"space-y-6"},up={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},dp=["value"],fp=["value"],pp=["value"],mp={class:"bg-gray-50 rounded-lg p-4"},hp={class:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm"},gp={class:"font-semibold"},xp={class:"font-semibold text-green-600"},yp={class:"font-semibold text-blue-600"},vp={class:"font-bold text-primary-600"},bp=Ot({__name:"PricingModal",setup(t){const e=Cs(),s=$s(),n=Ct("materials"),o=()=>{e.closePricingModal()},r=(f,u)=>{s.updateMaterialPrices({[f]:u})},l=(f,u)=>{s.updateLaborPrices({[f]:u})},a=(f,u)=>{s.updateProjectSettings({[f]:u})},c=()=>{s.resetToDefaults(),e.showSuccess("Başarılı","Fiyatlar varsayılan değerlere sıfırlandı")},p=()=>{e.showSuccess("Başarılı","Fiyat ayarları kaydedildi"),o()};return(f,u)=>x(e).pricingModalOpen?(F(),I("div",zf,[i("div",Wf,[i("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:o}),i("div",Vf,[i("div",{class:"bg-white px-6 pt-6 pb-4 border-b border-gray-200"},[i("div",{class:"flex items-center justify-between"},[u[18]||(u[18]=i("div",null,[i("h3",{class:"text-xl font-semibold text-gray-900",id:"modal-title"}," Fiyat Ayarları "),i("p",{class:"text-sm text-gray-500 mt-1"}," Malzeme ve işçilik birim fiyatlarını düzenleyin ")],-1)),i("button",{onClick:o,class:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg p-2"},u[17]||(u[17]=[i("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),i("div",Kf,[i("nav",Uf,[i("button",{onClick:u[0]||(u[0]=d=>n.value="materials"),class:Ft(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",n.value==="materials"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Malzeme Fiyatları ",2),i("button",{onClick:u[1]||(u[1]=d=>n.value="labor"),class:Ft(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",n.value==="labor"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," İşçilik Fiyatları ",2),i("button",{onClick:u[2]||(u[2]=d=>n.value="settings"),class:Ft(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",n.value==="settings"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Proje Ayarları ",2)])]),i("div",qf,[n.value==="materials"?(F(),I("div",Gf,[i("div",Yf,[i("div",null,[u[19]||(u[19]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Hazır Beton (TL/m³) ",-1)),i("input",{type:"number",value:x(s).materialPrices.readyMixConcrete,onInput:u[3]||(u[3]=d=>r("readyMixConcrete",Number(d.target.value))),min:"0",step:"10",class:"input-field"},null,40,Jf)]),i("div",null,[u[20]||(u[20]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," İnşaat Demiri (TL/ton) ",-1)),i("input",{type:"number",value:x(s).materialPrices.rebar,onInput:u[4]||(u[4]=d=>r("rebar",Number(d.target.value))),min:"0",step:"100",class:"input-field"},null,40,Qf)]),i("div",null,[u[21]||(u[21]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Sıva Harcı (TL/m²) ",-1)),i("input",{type:"number",value:x(s).materialPrices.plasterMortar,onInput:u[5]||(u[5]=d=>r("plasterMortar",Number(d.target.value))),min:"0",step:"1",class:"input-field"},null,40,Xf)]),i("div",null,[u[22]||(u[22]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Dış Cephe Boyası (TL/m²) ",-1)),i("input",{type:"number",value:x(s).materialPrices.exteriorPaint,onInput:u[6]||(u[6]=d=>r("exteriorPaint",Number(d.target.value))),min:"0",step:"1",class:"input-field"},null,40,Zf)]),i("div",null,[u[23]||(u[23]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Harpuşta (TL/m) ",-1)),i("input",{type:"number",value:x(s).materialPrices.coping,onInput:u[7]||(u[7]=d=>r("coping",Number(d.target.value))),min:"0",step:"5",class:"input-field"},null,40,tp)])])])):ot("",!0),n.value==="labor"?(F(),I("div",ep,[i("div",sp,[i("div",null,[u[24]||(u[24]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Temel Kazısı (TL/m³) ",-1)),i("input",{type:"number",value:x(s).laborPrices.excavation,onInput:u[8]||(u[8]=d=>l("excavation",Number(d.target.value))),min:"0",step:"1",class:"input-field"},null,40,np)]),i("div",null,[u[25]||(u[25]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Kalıp İşçiliği (TL/m²) ",-1)),i("input",{type:"number",value:x(s).laborPrices.formwork,onInput:u[9]||(u[9]=d=>l("formwork",Number(d.target.value))),min:"0",step:"5",class:"input-field"},null,40,op)]),i("div",null,[u[26]||(u[26]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Beton Dökümü (TL/m³) ",-1)),i("input",{type:"number",value:x(s).laborPrices.concretePouring,onInput:u[10]||(u[10]=d=>l("concretePouring",Number(d.target.value))),min:"0",step:"10",class:"input-field"},null,40,ip)]),i("div",null,[u[27]||(u[27]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Demir Bağlama (TL/ton) ",-1)),i("input",{type:"number",value:x(s).laborPrices.rebarInstallation,onInput:u[11]||(u[11]=d=>l("rebarInstallation",Number(d.target.value))),min:"0",step:"100",class:"input-field"},null,40,rp)]),i("div",null,[u[28]||(u[28]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Sıva İşçiliği (TL/m²) ",-1)),i("input",{type:"number",value:x(s).laborPrices.plastering,onInput:u[12]||(u[12]=d=>l("plastering",Number(d.target.value))),min:"0",step:"5",class:"input-field"},null,40,lp)]),i("div",null,[u[29]||(u[29]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Boya İşçiliği (TL/m²) ",-1)),i("input",{type:"number",value:x(s).laborPrices.painting,onInput:u[13]||(u[13]=d=>l("painting",Number(d.target.value))),min:"0",step:"1",class:"input-field"},null,40,ap)])])])):ot("",!0),n.value==="settings"?(F(),I("div",cp,[i("div",up,[i("div",null,[u[30]||(u[30]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Fire Payı (%) ",-1)),i("input",{type:"number",value:x(s).projectSettings.wastagePercentage,onInput:u[14]||(u[14]=d=>a("wastagePercentage",Number(d.target.value))),min:"0",max:"50",step:"1",class:"input-field"},null,40,dp),u[31]||(u[31]=i("p",{class:"text-xs text-gray-500 mt-1"},"Malzeme zayiatı için eklenen oran",-1))]),i("div",null,[u[32]||(u[32]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Kar Marjı (%) ",-1)),i("input",{type:"number",value:x(s).projectSettings.profitMargin,onInput:u[15]||(u[15]=d=>a("profitMargin",Number(d.target.value))),min:"0",max:"100",step:"1",class:"input-field"},null,40,fp),u[33]||(u[33]=i("p",{class:"text-xs text-gray-500 mt-1"},"Toplam maliyete eklenen kar oranı",-1))]),i("div",null,[u[34]||(u[34]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," KDV (%) ",-1)),i("input",{type:"number",value:x(s).projectSettings.vatPercentage,onInput:u[16]||(u[16]=d=>a("vatPercentage",Number(d.target.value))),min:"0",max:"50",step:"1",class:"input-field"},null,40,pp),u[35]||(u[35]=i("p",{class:"text-xs text-gray-500 mt-1"},"Katma Değer Vergisi oranı",-1))])]),i("div",mp,[u[40]||(u[40]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Mevcut Hesaplama Özeti",-1)),i("div",hp,[i("div",null,[u[36]||(u[36]=i("span",{class:"text-gray-600"},"Ara Toplam:",-1)),i("div",gp,"₺"+b(x(s).finalPricing.baseTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",null,[u[37]||(u[37]=i("span",{class:"text-gray-600"},"Kar Marjı:",-1)),i("div",xp,"₺"+b(x(s).finalPricing.profitAmount.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",null,[u[38]||(u[38]=i("span",{class:"text-gray-600"},"KDV:",-1)),i("div",yp,"₺"+b(x(s).finalPricing.vatAmount.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",null,[u[39]||(u[39]=i("span",{class:"text-gray-600"},"Genel Toplam:",-1)),i("div",vp,"₺"+b(x(s).finalPricing.finalTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])])])])):ot("",!0)]),i("div",{class:"bg-gray-50 px-6 py-4 flex items-center justify-between"},[i("button",{onClick:c,class:"btn-secondary flex items-center space-x-2"},u[41]||(u[41]=[i("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),i("span",null,"Varsayılana Sıfırla",-1)])),i("div",{class:"flex space-x-3"},[i("button",{onClick:o,class:"btn-secondary"}," İptal "),i("button",{onClick:p,class:"btn-primary"}," Kaydet ")])])])])])):ot("",!0)}}),_p={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},wp={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},kp={class:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full"},Sp={class:"bg-gray-50 px-6"},Cp={class:"flex space-x-8","aria-label":"Tabs"},$p={class:"bg-white px-6 py-6 max-h-96 overflow-y-auto"},Pp={key:0,class:"space-y-6"},Tp={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Ep={class:"bg-blue-50 rounded-lg p-4 border border-blue-200"},Mp={class:"space-y-2 text-sm"},Rp={class:"flex justify-between"},Ap={class:"font-semibold"},Dp={class:"flex justify-between"},Op={class:"font-semibold"},jp={class:"flex justify-between"},Lp={class:"font-semibold"},Fp={class:"flex justify-between"},Ip={class:"font-semibold"},Bp={class:"bg-green-50 rounded-lg p-4 border border-green-200"},Np={class:"space-y-2 text-sm"},Hp={class:"flex justify-between"},zp={class:"font-semibold"},Wp={class:"flex justify-between"},Vp={class:"font-semibold"},Kp={class:"flex justify-between"},Up={class:"font-semibold"},qp={class:"flex justify-between border-t border-green-300 pt-2"},Gp={class:"font-bold text-green-800"},Yp={class:"bg-purple-50 rounded-lg p-4 border border-purple-200"},Jp={class:"space-y-2 text-sm"},Qp={class:"flex justify-between"},Xp={class:"font-semibold"},Zp={class:"flex justify-between"},tm={class:"font-semibold"},em={class:"flex justify-between"},sm={class:"font-semibold"},nm={class:"flex justify-between"},om={class:"font-semibold"},im={class:"bg-gray-50 rounded-lg p-6"},rm={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},lm={class:"text-center"},am={class:"w-16 h-16 mx-auto mb-2 rounded-full bg-blue-500 flex items-center justify-center"},cm={class:"text-white font-bold text-sm"},um={class:"text-center"},dm={class:"w-16 h-16 mx-auto mb-2 rounded-full bg-orange-500 flex items-center justify-center"},fm={class:"text-white font-bold text-sm"},pm={class:"text-center"},mm={class:"w-16 h-16 mx-auto mb-2 rounded-full bg-green-500 flex items-center justify-center"},hm={class:"text-white font-bold text-sm"},gm={class:"text-center"},xm={class:"w-16 h-16 mx-auto mb-2 rounded-full bg-purple-500 flex items-center justify-center"},ym={class:"text-white font-bold text-sm"},vm={key:1,class:"space-y-6"},bm={class:"overflow-x-auto"},_m={class:"min-w-full divide-y divide-gray-200"},wm={class:"bg-white divide-y divide-gray-200"},km={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Sm={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Cm={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},$m={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Pm={class:"px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900"},Tm={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Em={class:"bg-gray-50"},Mm={class:"px-6 py-4 text-sm font-bold text-primary-600"},Rm={key:2,class:"space-y-6"},Am={class:"overflow-x-auto"},Dm={class:"min-w-full divide-y divide-gray-200"},Om={class:"bg-white divide-y divide-gray-200"},jm={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Lm={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Fm={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Im={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Bm={class:"px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900"},Nm={class:"bg-gray-50"},Hm={class:"px-6 py-4 text-sm font-bold text-primary-600"},zm={key:3,class:"space-y-6"},Wm={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Vm={class:"bg-blue-50 rounded-lg p-4 border border-blue-200"},Km={class:"space-y-2 text-sm"},Um={class:"flex justify-between"},qm={class:"font-semibold"},Gm={class:"flex justify-between"},Ym={class:"font-semibold"},Jm={class:"flex justify-between"},Qm={class:"font-semibold"},Xm={class:"flex justify-between"},Zm={class:"font-semibold"},th={class:"flex justify-between"},eh={class:"font-semibold"},sh={class:"bg-orange-50 rounded-lg p-4 border border-orange-200"},nh={class:"space-y-3"},oh={class:"text-sm space-y-1"},ih={class:"text-sm space-y-1"},rh={class:"text-sm space-y-1"},lh={class:"bg-green-50 rounded-lg p-4 border border-green-200"},ah={class:"space-y-2 text-sm"},ch={class:"flex justify-between"},uh={class:"font-semibold"},dh={class:"flex justify-between"},fh={class:"font-semibold"},ph={class:"flex justify-between"},mh={class:"font-semibold"},hh={class:"bg-purple-50 rounded-lg p-4 border border-purple-200"},gh={class:"space-y-2 text-sm"},xh={class:"flex justify-between"},yh={class:"font-semibold"},vh={class:"flex justify-between"},bh={class:"font-semibold"},_h={class:"flex justify-between"},wh={class:"font-semibold"},kh=Ot({__name:"ReportModal",setup(t){const e=Cs(),s=ne(),n=$s(),o=Ct("summary"),r=()=>{e.closeReportModal()},l=()=>{window.print()},a=()=>{e.showInfo("Bilgi","PDF export özelliği yakında eklenecek")},c=xt(()=>({totalArea:s.dimensions.length*s.dimensions.height,totalVolume:s.volumeCalculations.totalConcrete,totalWeight:s.rebarCalculations.totalWeightTons,unitCostPerM2:n.unitCosts.perSquareMeter,unitCostPerM3:n.unitCosts.perCubicMeter,efficiency:{materialToLaborRatio:(n.costBreakdown.materials.total/n.costBreakdown.labor.total).toFixed(2),concreteToRebarRatio:(n.costBreakdown.materials.concrete/n.costBreakdown.materials.rebar).toFixed(2)}})),p=xt(()=>[{name:"Hazır Beton C25/30",quantity:s.volumeCalculations.totalConcrete*(1+n.projectSettings.wastagePercentage/100),unit:"m³",unitPrice:n.materialPrices.readyMixConcrete,total:n.costBreakdown.materials.concrete,percentage:(n.costBreakdown.materials.concrete/n.costBreakdown.materials.total*100).toFixed(1)},{name:"İnşaat Demiri S420",quantity:s.rebarCalculations.totalWeightTons*(1+n.projectSettings.wastagePercentage/100),unit:"ton",unitPrice:n.materialPrices.rebar,total:n.costBreakdown.materials.rebar,percentage:(n.costBreakdown.materials.rebar/n.costBreakdown.materials.total*100).toFixed(1)},...s.surfaceOptions.plaster.enabled?[{name:"Sıva Harcı",quantity:s.volumeCalculations.plasterArea*(1+n.projectSettings.wastagePercentage/100),unit:"m²",unitPrice:n.materialPrices.plasterMortar,total:n.costBreakdown.materials.plaster,percentage:(n.costBreakdown.materials.plaster/n.costBreakdown.materials.total*100).toFixed(1)}]:[],...s.surfaceOptions.paint.enabled?[{name:"Dış Cephe Boyası",quantity:s.volumeCalculations.paintArea*(1+n.projectSettings.wastagePercentage/100),unit:"m²",unitPrice:n.materialPrices.exteriorPaint,total:n.costBreakdown.materials.paint,percentage:(n.costBreakdown.materials.paint/n.costBreakdown.materials.total*100).toFixed(1)}]:[],...s.surfaceOptions.coping?[{name:"Harpuşta",quantity:s.dimensions.length*(1+n.projectSettings.wastagePercentage/100),unit:"m",unitPrice:n.materialPrices.coping,total:n.costBreakdown.materials.coping,percentage:(n.costBreakdown.materials.coping/n.costBreakdown.materials.total*100).toFixed(1)}]:[]]),f=xt(()=>[{name:"Temel Kazısı",quantity:s.volumeCalculations.foundationConcrete,unit:"m³",unitPrice:n.laborPrices.excavation,total:n.costBreakdown.labor.excavation},{name:"Kalıp İşçiliği",quantity:2*s.dimensions.length*(s.dimensions.foundationDepth/100)+2*s.dimensions.length*s.dimensions.height,unit:"m²",unitPrice:n.laborPrices.formwork,total:n.costBreakdown.labor.formwork},{name:"Beton Dökümü",quantity:s.volumeCalculations.totalConcrete,unit:"m³",unitPrice:n.laborPrices.concretePouring,total:n.costBreakdown.labor.concretePouring},{name:"Demir Bağlama",quantity:s.rebarCalculations.totalWeightTons,unit:"ton",unitPrice:n.laborPrices.rebarInstallation,total:n.costBreakdown.labor.rebarInstallation},...s.surfaceOptions.plaster.enabled?[{name:"Sıva İşçiliği",quantity:s.volumeCalculations.plasterArea,unit:"m²",unitPrice:n.laborPrices.plastering,total:n.costBreakdown.labor.plastering}]:[],...s.surfaceOptions.paint.enabled?[{name:"Boya İşçiliği",quantity:s.volumeCalculations.paintArea,unit:"m²",unitPrice:n.laborPrices.painting,total:n.costBreakdown.labor.painting}]:[]]);return(u,d)=>x(e).reportModalOpen?(F(),I("div",_p,[i("div",wp,[i("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:r}),i("div",kp,[i("div",{class:"bg-white px-6 pt-6 pb-4 border-b border-gray-200"},[i("div",{class:"flex items-center justify-between"},[d[7]||(d[7]=i("div",null,[i("h3",{class:"text-xl font-semibold text-gray-900",id:"modal-title"}," Perde Duvar Analiz Raporu "),i("p",{class:"text-sm text-gray-500 mt-1"}," Detaylı maliyet analizi ve teknik özellikler ")],-1)),i("div",{class:"flex items-center space-x-3"},[i("button",{onClick:l,class:"btn-secondary flex items-center space-x-2"},d[4]||(d[4]=[i("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"})],-1),i("span",null,"Yazdır",-1)])),i("button",{onClick:a,class:"btn-secondary flex items-center space-x-2"},d[5]||(d[5]=[i("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),i("span",null,"PDF",-1)])),i("button",{onClick:r,class:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg p-2"},d[6]||(d[6]=[i("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])]),i("div",Sp,[i("nav",Cp,[i("button",{onClick:d[0]||(d[0]=g=>o.value="summary"),class:Ft(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",o.value==="summary"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Özet ",2),i("button",{onClick:d[1]||(d[1]=g=>o.value="materials"),class:Ft(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",o.value==="materials"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Malzeme Detayı ",2),i("button",{onClick:d[2]||(d[2]=g=>o.value="labor"),class:Ft(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",o.value==="labor"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," İşçilik Detayı ",2),i("button",{onClick:d[3]||(d[3]=g=>o.value="technical"),class:Ft(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",o.value==="technical"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Teknik Özellikler ",2)])]),i("div",$p,[o.value==="summary"?(F(),I("div",Pp,[i("div",Tp,[i("div",Ep,[d[12]||(d[12]=i("h4",{class:"text-lg font-semibold text-blue-900 mb-3"},"Proje Özeti",-1)),i("div",Mp,[i("div",Rp,[d[8]||(d[8]=i("span",{class:"text-blue-700"},"Duvar Boyutu:",-1)),i("span",Ap,b(x(s).dimensions.length)+"m × "+b(x(s).dimensions.height)+"m",1)]),i("div",Dp,[d[9]||(d[9]=i("span",{class:"text-blue-700"},"Toplam Alan:",-1)),i("span",Op,b(c.value.totalArea.toFixed(2))+" m²",1)]),i("div",jp,[d[10]||(d[10]=i("span",{class:"text-blue-700"},"Toplam Hacim:",-1)),i("span",Lp,b(c.value.totalVolume.toFixed(2))+" m³",1)]),i("div",Fp,[d[11]||(d[11]=i("span",{class:"text-blue-700"},"Toplam Demir:",-1)),i("span",Ip,b(c.value.totalWeight.toFixed(2))+" ton",1)])])]),i("div",Bp,[d[17]||(d[17]=i("h4",{class:"text-lg font-semibold text-green-900 mb-3"},"Maliyet Özeti",-1)),i("div",Np,[i("div",Hp,[d[13]||(d[13]=i("span",{class:"text-green-700"},"Malzeme:",-1)),i("span",zp,"₺"+b(x(n).costBreakdown.materials.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Wp,[d[14]||(d[14]=i("span",{class:"text-green-700"},"İşçilik:",-1)),i("span",Vp,"₺"+b(x(n).costBreakdown.labor.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Kp,[d[15]||(d[15]=i("span",{class:"text-green-700"},"Kar + KDV:",-1)),i("span",Up,"₺"+b((x(n).finalPricing.profitAmount+x(n).finalPricing.vatAmount).toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",qp,[d[16]||(d[16]=i("span",{class:"text-green-700 font-semibold"},"TOPLAM:",-1)),i("span",Gp,"₺"+b(x(n).finalPricing.finalTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])])]),i("div",Yp,[d[22]||(d[22]=i("h4",{class:"text-lg font-semibold text-purple-900 mb-3"},"Birim Maliyetler",-1)),i("div",Jp,[i("div",Qp,[d[18]||(d[18]=i("span",{class:"text-purple-700"},"m² başına:",-1)),i("span",Xp,"₺"+b(c.value.unitCostPerM2.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Zp,[d[19]||(d[19]=i("span",{class:"text-purple-700"},"m³ başına:",-1)),i("span",tm,"₺"+b(c.value.unitCostPerM3.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",em,[d[20]||(d[20]=i("span",{class:"text-purple-700"},"Malz./İşç. Oranı:",-1)),i("span",sm,b(c.value.efficiency.materialToLaborRatio),1)]),i("div",nm,[d[21]||(d[21]=i("span",{class:"text-purple-700"},"Beton/Demir Oranı:",-1)),i("span",om,b(c.value.efficiency.concreteToRebarRatio),1)])])])]),i("div",im,[d[27]||(d[27]=i("h4",{class:"text-lg font-semibold text-gray-900 mb-4"},"Maliyet Dağılımı",-1)),i("div",rm,[i("div",lm,[i("div",am,[i("span",cm,b((x(n).costBreakdown.materials.total/x(n).finalPricing.baseTotal*100).toFixed(0))+"%",1)]),d[23]||(d[23]=i("div",{class:"text-sm text-gray-600"},"Malzeme",-1))]),i("div",um,[i("div",dm,[i("span",fm,b((x(n).costBreakdown.labor.total/x(n).finalPricing.baseTotal*100).toFixed(0))+"%",1)]),d[24]||(d[24]=i("div",{class:"text-sm text-gray-600"},"İşçilik",-1))]),i("div",pm,[i("div",mm,[i("span",hm,b(x(n).projectSettings.profitMargin)+"%",1)]),d[25]||(d[25]=i("div",{class:"text-sm text-gray-600"},"Kar Marjı",-1))]),i("div",gm,[i("div",xm,[i("span",ym,b(x(n).projectSettings.vatPercentage)+"%",1)]),d[26]||(d[26]=i("div",{class:"text-sm text-gray-600"},"KDV",-1))])])])])):ot("",!0),o.value==="materials"?(F(),I("div",vm,[i("div",bm,[i("table",_m,[d[30]||(d[30]=i("thead",{class:"bg-gray-50"},[i("tr",null,[i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Malzeme"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Miktar"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Birim"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Birim Fiyat"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Toplam"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Oran")])],-1)),i("tbody",wm,[(F(!0),I(gt,null,Dt(p.value,(g,k)=>(F(),I("tr",{key:k,class:"hover:bg-gray-50"},[i("td",km,b(g.name),1),i("td",Sm,b(g.quantity.toFixed(2)),1),i("td",Cm,b(g.unit),1),i("td",$m,"₺"+b(g.unitPrice.toLocaleString("tr-TR")),1),i("td",Pm,"₺"+b(g.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1),i("td",Tm,"%"+b(g.percentage),1)]))),128))]),i("tfoot",Em,[i("tr",null,[d[28]||(d[28]=i("td",{colspan:"4",class:"px-6 py-4 text-sm font-semibold text-gray-900"},"TOPLAM MALZEME MALİYETİ",-1)),i("td",Mm,"₺"+b(x(n).costBreakdown.materials.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1),d[29]||(d[29]=i("td",{class:"px-6 py-4 text-sm font-semibold text-gray-900"},"%100",-1))])])])])])):ot("",!0),o.value==="labor"?(F(),I("div",Rm,[i("div",Am,[i("table",Dm,[d[32]||(d[32]=i("thead",{class:"bg-gray-50"},[i("tr",null,[i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"İşçilik"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Miktar"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Birim"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Birim Fiyat"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Toplam")])],-1)),i("tbody",Om,[(F(!0),I(gt,null,Dt(f.value,(g,k)=>(F(),I("tr",{key:k,class:"hover:bg-gray-50"},[i("td",jm,b(g.name),1),i("td",Lm,b(g.quantity.toFixed(2)),1),i("td",Fm,b(g.unit),1),i("td",Im,"₺"+b(g.unitPrice.toLocaleString("tr-TR")),1),i("td",Bm,"₺"+b(g.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]))),128))]),i("tfoot",Nm,[i("tr",null,[d[31]||(d[31]=i("td",{colspan:"4",class:"px-6 py-4 text-sm font-semibold text-gray-900"},"TOPLAM İŞÇİLİK MALİYETİ",-1)),i("td",Hm,"₺"+b(x(n).costBreakdown.labor.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])])])])])):ot("",!0),o.value==="technical"?(F(),I("div",zm,[i("div",Wm,[i("div",Vm,[d[38]||(d[38]=i("h4",{class:"text-lg font-semibold text-blue-900 mb-3"},"Boyutlar",-1)),i("div",Km,[i("div",Um,[d[33]||(d[33]=i("span",{class:"text-blue-700"},"Duvar Uzunluğu:",-1)),i("span",qm,b(x(s).dimensions.length)+" m",1)]),i("div",Gm,[d[34]||(d[34]=i("span",{class:"text-blue-700"},"Duvar Yüksekliği:",-1)),i("span",Ym,b(x(s).dimensions.height)+" m",1)]),i("div",Jm,[d[35]||(d[35]=i("span",{class:"text-blue-700"},"Duvar Kalınlığı:",-1)),i("span",Qm,b(x(s).dimensions.thickness)+" cm",1)]),i("div",Xm,[d[36]||(d[36]=i("span",{class:"text-blue-700"},"Temel Derinliği:",-1)),i("span",Zm,b(x(s).dimensions.foundationDepth)+" cm",1)]),i("div",th,[d[37]||(d[37]=i("span",{class:"text-blue-700"},"Temel Genişliği:",-1)),i("span",eh,b(x(s).dimensions.foundationWidth)+" cm",1)])])]),i("div",sh,[d[42]||(d[42]=i("h4",{class:"text-lg font-semibold text-orange-900 mb-3"},"Donatı Detayları",-1)),i("div",nh,[i("div",null,[d[39]||(d[39]=i("h5",{class:"font-medium text-orange-800 mb-1"},"Temel Donatısı",-1)),i("div",oh,[i("div",null,"Boyuna: Ø"+b(x(s).rebarDetails.foundation.longitudinalDiameter)+"mm × "+b(x(s).rebarDetails.foundation.longitudinalCount)+" adet",1),i("div",null,"Etriye: Ø"+b(x(s).rebarDetails.foundation.stirrupDiameter)+"mm / "+b(x(s).rebarDetails.foundation.stirrupSpacing)+"cm",1)])]),i("div",null,[d[40]||(d[40]=i("h5",{class:"font-medium text-orange-800 mb-1"},"Duvar Donatısı",-1)),i("div",ih,[i("div",null,"Dikey: Ø"+b(x(s).rebarDetails.wall.verticalDiameter)+"mm / "+b(x(s).rebarDetails.wall.verticalSpacing)+"cm",1),i("div",null,"Yatay: Ø"+b(x(s).rebarDetails.wall.horizontalDiameter)+"mm / "+b(x(s).rebarDetails.wall.horizontalSpacing)+"cm",1)])]),i("div",null,[d[41]||(d[41]=i("h5",{class:"font-medium text-orange-800 mb-1"},"Genel",-1)),i("div",rh,[i("div",null,"Paspay: "+b(x(s).rebarDetails.general.concreteCover)+"cm",1),i("div",null,"Filiz Payı: "+b(x(s).rebarDetails.general.foundationWallExtension)+"cm",1)])])])]),i("div",lh,[d[46]||(d[46]=i("h4",{class:"text-lg font-semibold text-green-900 mb-3"},"Yüzey İşlemleri",-1)),i("div",ah,[i("div",ch,[d[43]||(d[43]=i("span",{class:"text-green-700"},"Harpuşta:",-1)),i("span",uh,b(x(s).surfaceOptions.coping?"Evet":"Hayır"),1)]),i("div",dh,[d[44]||(d[44]=i("span",{class:"text-green-700"},"Sıva:",-1)),i("span",fh,b(x(s).surfaceOptions.plaster.enabled?x(s).surfaceOptions.plaster.doubleSided?"Çift Yüzey":"Tek Yüzey":"Hayır"),1)]),i("div",ph,[d[45]||(d[45]=i("span",{class:"text-green-700"},"Boya:",-1)),i("span",mh,b(x(s).surfaceOptions.paint.enabled?x(s).surfaceOptions.paint.doubleSided?"Çift Yüzey":"Tek Yüzey":"Hayır"),1)])])]),i("div",hh,[d[50]||(d[50]=i("h4",{class:"text-lg font-semibold text-purple-900 mb-3"},"Proje Ayarları",-1)),i("div",gh,[i("div",xh,[d[47]||(d[47]=i("span",{class:"text-purple-700"},"Fire Payı:",-1)),i("span",yh,"%"+b(x(n).projectSettings.wastagePercentage),1)]),i("div",vh,[d[48]||(d[48]=i("span",{class:"text-purple-700"},"Kar Marjı:",-1)),i("span",bh,"%"+b(x(n).projectSettings.profitMargin),1)]),i("div",_h,[d[49]||(d[49]=i("span",{class:"text-purple-700"},"KDV:",-1)),i("span",wh,"%"+b(x(n).projectSettings.vatPercentage),1)])])])])])):ot("",!0)]),i("div",{class:"bg-gray-50 px-6 py-4 flex items-center justify-end"},[i("button",{onClick:r,class:"btn-primary"}," Kapat ")])])])])):ot("",!0)}}),Sh={class:"min-h-screen bg-gray-50"},Ch={class:"pt-32"},$h={class:"min-h-full p-4 lg:p-6"},Ph={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Th=Ot({__name:"App",setup(t){const e=Cs();return(s,n)=>(F(),I("div",Sh,[it(Pf),i("main",Ch,[i("div",$h,[it(x(zr))])]),it(bp),it(kh),it(Hf),x(e).loading?(F(),I("div",Ph,n[0]||(n[0]=[i("div",{class:"bg-white rounded-lg p-6 flex items-center space-x-3"},[i("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),i("span",{class:"text-gray-700"},"Hesaplanıyor...")],-1)]))):ot("",!0)]))}}),Eh="modulepreload",Mh=function(t){return"/"+t},ci={},Rh=function(e,s,n){let o=Promise.resolve();if(s&&s.length>0){let c=function(p){return Promise.all(p.map(f=>Promise.resolve(f).then(u=>({status:"fulfilled",value:u}),u=>({status:"rejected",reason:u}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),a=l?.nonce||l?.getAttribute("nonce");o=c(s.map(p=>{if(p=Mh(p),p in ci)return;ci[p]=!0;const f=p.endsWith(".css"),u=f?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${u}`))return;const d=document.createElement("link");if(d.rel=f?"stylesheet":Eh,f||(d.as="script"),d.crossOrigin="",d.href=p,a&&d.setAttribute("nonce",a),document.head.appendChild(d),f)return new Promise((g,k)=>{d.addEventListener("load",g),d.addEventListener("error",()=>k(new Error(`Unable to preload CSS for ${p}`)))})}))}function r(l){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=l,window.dispatchEvent(a),!a.defaultPrevented)throw l}return o.then(l=>{for(const a of l||[])a.status==="rejected"&&r(a.reason);return e().catch(r)})},Ah={class:"w-full"},Dh=["x","y","width","height"],Oh=["x","y","width","height"],jh=["x","y"],Lh=["x","y","width","height"],Fh=["x","y","width","height"],Ih=["x","y"],Bh={key:0},Nh=["x1","y1","x2","y2","stroke"],Hh=["x","y","width"],zh={key:2},Wh=["x","y","height"],Vh=["x","y","height"],Kh=["x1","y1","x2","y2"],Uh=["cx","cy"],qh=["x1","y1","x2","y2"],Gh=["x","y"],Yh=["x","y"],Jh=["x","y"],Qh={transform:"translate(20, 20)"},Xh={key:0,x:"10",y:"85",width:"12",height:"8",fill:"#10b981",opacity:"0.6"},Zh={key:1,x:"28",y:"92",class:"text-xs fill-gray-700"},t0={key:2,x:"10",y:"100",width:"12",height:"8",fill:"#fbbf24"},e0={key:3,x:"28",y:"107",class:"text-xs fill-gray-700"},bn=800,_n=500,ui=50,s0=Ot({__name:"WallVisualization",setup(t){const e=ne(),s=xt(()=>{const{length:a,height:c,thickness:p,foundationDepth:f,foundationWidth:u}=e.dimensions,d=Math.max(a,u/100),g=c+f/100,k=(bn-2*ui)/d,T=(_n-2*ui)/g,N=Math.min(k,T);return{scale:N,wallHeight:c*N,wallThickness:p/100*N,foundationDepth:f/100*N,foundationWidth:u/100*N,centerX:bn/2,centerY:_n/2}}),n=xt(()=>{const{wallHeight:a,wallThickness:c,foundationDepth:p,foundationWidth:f,centerX:u,centerY:d}=s.value,g=u-f/2,k=d+a/2,T=u-c/2,N=k-a;return{foundation:{x:g,y:k,width:f,height:p},wall:{x:T,y:N,width:c,height:a}}}),o=xt(()=>{const{wall:a}=n.value,{verticalSpacing:c,horizontalSpacing:p}=e.rebarDetails.wall,{concreteCover:f}=e.rebarDetails.general,{scale:u}=s.value,d=f/100*u,g=[],k=c/100*u,T=Math.floor(a.width/k)+1;for(let C=0;C<T;C++){const P=a.x+d+C*k;P<=a.x+a.width-d&&g.push({x1:P,y1:a.y+d,x2:P,y2:a.y+a.height-d,type:"vertical"})}const N=p/100*u,B=Math.floor(a.height/N)+1;for(let C=0;C<B;C++){const P=a.y+d+C*N;P<=a.y+a.height-d&&g.push({x1:a.x+d,y1:P,x2:a.x+a.width-d,y2:P,type:"horizontal"})}return g}),r=xt(()=>{const{wall:a,foundation:c}=n.value,{length:p,height:f,thickness:u,foundationDepth:d,foundationWidth:g}=e.dimensions;return[{x:a.x+a.width/2,y:a.y-30,text:`${p}m`,type:"length",line:{x1:a.x,y1:a.y-15,x2:a.x+a.width,y2:a.y-15}},{x:a.x-40,y:a.y+a.height/2,text:`${f}m`,type:"height",line:{x1:a.x-25,y1:a.y,x2:a.x-25,y2:a.y+a.height}},{x:a.x+a.width+30,y:a.y+a.height/2,text:`${u}cm`,type:"thickness",line:{x1:a.x+a.width+15,y1:a.y,x2:a.x+a.width+15,y2:a.y+a.height}},{x:c.x-40,y:c.y+c.height/2,text:`${d}cm`,type:"foundation",line:{x1:c.x-25,y1:c.y,x2:c.x-25,y2:c.y+c.height}},{x:c.x+c.width/2,y:c.y+c.height+30,text:`${g}cm`,type:"foundation",line:{x1:c.x,y1:c.y+c.height+15,x2:c.x+c.width,y2:c.y+c.height+15}}]}),l=xt(()=>{const{foundation:a}=n.value;return{x1:a.x-50,y1:a.y+a.height,x2:a.x+a.width+50,y2:a.y+a.height}});return(a,c)=>(F(),I("div",Ah,[(F(),I("svg",{width:bn,height:_n,class:"w-full h-auto border border-gray-200 rounded-lg bg-gray-50",viewBox:"0 0 800 500",preserveAspectRatio:"xMidYMid meet"},[c[1]||(c[1]=ce('<defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" stroke-width="0.5"></path></pattern><marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6"></polygon></marker><pattern id="concrete" patternUnits="userSpaceOnUse" width="4" height="4"><rect width="4" height="4" fill="#f3f4f6"></rect><circle cx="2" cy="2" r="0.5" fill="#d1d5db"></circle></pattern><pattern id="foundation" patternUnits="userSpaceOnUse" width="6" height="6"><rect width="6" height="6" fill="#d1d5db"></rect><circle cx="3" cy="3" r="0.8" fill="#9ca3af"></circle></pattern><linearGradient id="plasterGradient" x1="0%" y1="0%" x2="100%" y2="0%"><stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8;"></stop><stop offset="100%" style="stop-color:#10b981;stop-opacity:0.4;"></stop></linearGradient></defs><rect width="100%" height="100%" fill="url(#grid)"></rect>',2)),i("rect",{x:n.value.foundation.x,y:n.value.foundation.y,width:n.value.foundation.width,height:n.value.foundation.height,fill:"url(#foundation)",stroke:"#9ca3af","stroke-width":"2",rx:"3"},null,8,Dh),i("rect",{x:n.value.foundation.x,y:n.value.foundation.y,width:n.value.foundation.width,height:n.value.foundation.height,fill:"none",stroke:"#6b7280","stroke-width":"1",rx:"3",opacity:"0.5"},null,8,Oh),i("text",{x:n.value.foundation.x+n.value.foundation.width/2,y:n.value.foundation.y+n.value.foundation.height/2,"text-anchor":"middle","dominant-baseline":"middle",class:"text-sm font-bold fill-gray-700",style:{"text-shadow":"1px 1px 2px rgba(255,255,255,0.8)"}}," TEMEL ",8,jh),i("rect",{x:n.value.wall.x,y:n.value.wall.y,width:n.value.wall.width,height:n.value.wall.height,fill:"url(#concrete)",stroke:"#6b7280","stroke-width":"2",rx:"3"},null,8,Lh),i("rect",{x:n.value.wall.x,y:n.value.wall.y,width:n.value.wall.width,height:n.value.wall.height,fill:"none",stroke:"#4b5563","stroke-width":"1",rx:"3",opacity:"0.7"},null,8,Fh),i("text",{x:n.value.wall.x+n.value.wall.width/2,y:n.value.wall.y+n.value.wall.height/2,"text-anchor":"middle","dominant-baseline":"middle",class:"text-sm font-bold fill-gray-800",style:{"text-shadow":"1px 1px 2px rgba(255,255,255,0.8)"}}," PERDE DUVAR ",8,Ih),o.value.length>0?(F(),I("g",Bh,[(F(!0),I(gt,null,Dt(o.value,(p,f)=>(F(),I("line",{key:f,x1:p.x1,y1:p.y1,x2:p.x2,y2:p.y2,stroke:p.type==="vertical"?"#dc2626":"#ea580c","stroke-width":"1.5",opacity:"0.8"},null,8,Nh))),128))])):ot("",!0),x(e).surfaceOptions.coping?(F(),I("rect",{key:1,x:n.value.wall.x-5,y:n.value.wall.y-15,width:n.value.wall.width+10,height:"15",fill:"#fbbf24",stroke:"#f59e0b","stroke-width":"1",rx:"2"},null,8,Hh)):ot("",!0),x(e).surfaceOptions.plaster.enabled?(F(),I("g",zh,[i("rect",{x:n.value.wall.x-4,y:n.value.wall.y-2,width:"4",height:n.value.wall.height+4,fill:"url(#plasterGradient)",stroke:"#059669","stroke-width":"0.5",rx:"2"},null,8,Wh),x(e).surfaceOptions.plaster.doubleSided?(F(),I("rect",{key:0,x:n.value.wall.x+n.value.wall.width,y:n.value.wall.y-2,width:"4",height:n.value.wall.height+4,fill:"url(#plasterGradient)",stroke:"#059669","stroke-width":"0.5",rx:"2"},null,8,Vh)):ot("",!0)])):ot("",!0),i("line",{x1:l.value.x1,y1:l.value.y1,x2:l.value.x2,y2:l.value.y2,stroke:"#8b5cf6","stroke-width":"3","stroke-dasharray":"5,5"},null,8,Kh),i("g",null,[(F(),I(gt,null,Dt(8,p=>i("circle",{key:p,cx:l.value.x1+p*20,cy:l.value.y1+10,r:"2",fill:"#8b5cf6",opacity:"0.6"},null,8,Uh)),64))]),i("g",null,[(F(!0),I(gt,null,Dt(r.value,(p,f)=>(F(),I("line",{key:`line-${f}`,x1:p.line.x1,y1:p.line.y1,x2:p.line.x2,y2:p.line.y2,stroke:"#3b82f6","stroke-width":"2","marker-start":"url(#arrowhead)","marker-end":"url(#arrowhead)"},null,8,qh))),128)),(F(!0),I(gt,null,Dt(r.value,(p,f)=>(F(),I("text",{key:`text-${f}`,x:p.x,y:p.y,"text-anchor":"middle","dominant-baseline":"middle",class:"text-xs font-bold fill-primary-600"},b(p.text),9,Gh))),128)),(F(!0),I(gt,null,Dt(r.value,(p,f)=>(F(),I("rect",{key:`bg-${f}`,x:p.x-15,y:p.y-8,width:"30",height:"16",fill:"white",stroke:"#3b82f6","stroke-width":"1",rx:"3",opacity:"0.9"},null,8,Yh))),128)),(F(!0),I(gt,null,Dt(r.value,(p,f)=>(F(),I("text",{key:`text-top-${f}`,x:p.x,y:p.y,"text-anchor":"middle","dominant-baseline":"middle",class:"text-xs font-bold fill-primary-600"},b(p.text),9,Jh))),128))]),i("g",Qh,[c[0]||(c[0]=ce('<rect x="0" y="0" width="150" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="4" opacity="0.95"></rect><text x="10" y="15" class="text-xs font-semibold fill-gray-900">Açıklama</text><rect x="10" y="25" width="12" height="8" fill="#d1d5db" stroke="#9ca3af"></rect><text x="28" y="32" class="text-xs fill-gray-700">Temel</text><rect x="10" y="40" width="12" height="8" fill="#f3f4f6" stroke="#6b7280"></rect><text x="28" y="47" class="text-xs fill-gray-700">Duvar</text><line x1="10" y1="55" x2="22" y2="55" stroke="#dc2626" stroke-width="2"></line><text x="28" y="58" class="text-xs fill-gray-700">Dikey Donatı</text><line x1="10" y1="70" x2="22" y2="70" stroke="#ea580c" stroke-width="2"></line><text x="28" y="73" class="text-xs fill-gray-700">Yatay Donatı</text>',10)),x(e).surfaceOptions.plaster.enabled?(F(),I("rect",Xh)):ot("",!0),x(e).surfaceOptions.plaster.enabled?(F(),I("text",Zh,"Sıva")):ot("",!0),x(e).surfaceOptions.coping?(F(),I("rect",t0)):ot("",!0),x(e).surfaceOptions.coping?(F(),I("text",e0,"Harpuşta")):ot("",!0)])]))]))}}),n0={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},o0={class:"space-y-4"},i0={class:"bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-4 border border-primary-200"},r0={class:"text-center"},l0={class:"text-3xl font-bold text-primary-600"},a0={class:"space-y-3"},c0={class:"flex items-center justify-between"},u0={class:"font-semibold text-gray-900"},d0={class:"flex items-center justify-between"},f0={class:"font-semibold text-gray-900"},p0={class:"flex items-center justify-between"},m0={class:"flex items-center space-x-2"},h0={class:"text-sm text-gray-600"},g0={class:"font-semibold text-gray-900"},x0={class:"flex items-center justify-between"},y0={class:"flex items-center space-x-2"},v0={class:"text-sm text-gray-600"},b0={class:"font-semibold text-gray-900"},_0={class:"border-t border-gray-200 pt-4"},w0={class:"grid grid-cols-1 gap-2 text-sm"},k0={class:"flex justify-between"},S0={class:"font-medium"},C0={class:"flex justify-between"},$0={class:"font-medium"},P0={class:"flex justify-between"},T0={class:"font-medium"},E0={class:"bg-gray-50 rounded-lg p-3"},M0={class:"flex items-center justify-between text-xs text-gray-600 mb-2"},R0={class:"w-full bg-gray-200 rounded-full h-2"},A0=Ot({__name:"CostSummaryCard",setup(t){const e=$s();return(s,n)=>(F(),I("div",n0,[n[10]||(n[10]=ce('<div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-gray-900">Maliyet Özeti</h3><div class="flex items-center space-x-1"><svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path></svg><span class="text-sm text-gray-500">Anlık Hesaplama</span></div></div>',1)),i("div",o0,[i("div",i0,[i("div",r0,[i("div",l0," ₺"+b(x(e).finalPricing.finalTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1),n[0]||(n[0]=i("div",{class:"text-sm text-gray-600 mt-1"},"Toplam Proje Maliyeti",-1))])]),i("div",a0,[i("div",c0,[n[1]||(n[1]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-blue-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"Malzeme")],-1)),i("span",u0," ₺"+b(x(e).costBreakdown.materials.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",d0,[n[2]||(n[2]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-orange-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"İşçilik")],-1)),i("span",f0," ₺"+b(x(e).costBreakdown.labor.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",p0,[i("div",m0,[n[3]||(n[3]=i("div",{class:"w-3 h-3 bg-green-500 rounded-full"},null,-1)),i("span",h0,"Kar Marjı (%"+b(x(e).projectSettings.profitMargin)+")",1)]),i("span",g0," ₺"+b(x(e).finalPricing.profitAmount.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",x0,[i("div",y0,[n[4]||(n[4]=i("div",{class:"w-3 h-3 bg-purple-500 rounded-full"},null,-1)),i("span",v0,"KDV (%"+b(x(e).projectSettings.vatPercentage)+")",1)]),i("span",b0," ₺"+b(x(e).finalPricing.vatAmount.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])]),i("div",_0,[n[8]||(n[8]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Birim Maliyetler",-1)),i("div",w0,[i("div",k0,[n[5]||(n[5]=i("span",{class:"text-gray-600"},"m² başına:",-1)),i("span",S0,"₺"+b(x(e).unitCosts.perSquareMeter.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",C0,[n[6]||(n[6]=i("span",{class:"text-gray-600"},"m³ başına:",-1)),i("span",$0,"₺"+b(x(e).unitCosts.perCubicMeter.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",P0,[n[7]||(n[7]=i("span",{class:"text-gray-600"},"Metre başına:",-1)),i("span",T0,"₺"+b(x(e).unitCosts.perLinearMeter.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])])]),i("div",E0,[i("div",M0,[n[9]||(n[9]=i("span",null,"Malzeme/İşçilik Oranı",-1)),i("span",null,b((x(e).costBreakdown.materials.total/x(e).costBreakdown.grandTotal*100).toFixed(0))+"% / "+b((x(e).costBreakdown.labor.total/x(e).costBreakdown.grandTotal*100).toFixed(0))+"%",1)]),i("div",R0,[i("div",{class:"bg-blue-500 h-2 rounded-l-full",style:ps({width:x(e).costBreakdown.materials.total/x(e).costBreakdown.grandTotal*100+"%"})},null,4),i("div",{class:"bg-orange-500 h-2 rounded-r-full -mt-2 ml-auto",style:ps({width:x(e).costBreakdown.labor.total/x(e).costBreakdown.grandTotal*100+"%"})},null,4)])])])]))}}),D0={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},O0={class:"space-y-4"},j0={class:"bg-blue-50 rounded-lg p-4 border border-blue-200"},L0={class:"grid grid-cols-2 gap-4 text-sm"},F0={class:"font-semibold ml-2"},I0={class:"font-semibold ml-2"},B0={key:0},N0={class:"font-semibold ml-2"},H0={class:"col-span-2 border-t border-blue-200 pt-2"},z0={class:"font-bold text-blue-600 ml-2"},W0={class:"bg-orange-50 rounded-lg p-4 border border-orange-200"},V0={class:"grid grid-cols-2 gap-4 text-sm"},K0={class:"font-semibold ml-2"},U0={class:"font-semibold ml-2"},q0={class:"col-span-2 border-t border-orange-200 pt-2"},G0={class:"font-bold text-orange-600 ml-2"},Y0={key:0,class:"space-y-3"},J0={key:0,class:"bg-green-50 rounded-lg p-4 border border-green-200"},Q0={class:"flex items-center justify-between mb-2"},X0={class:"text-sm text-green-600 font-medium"},Z0={class:"text-sm"},tg={class:"font-bold text-green-600 ml-2"},eg={key:1,class:"bg-purple-50 rounded-lg p-4 border border-purple-200"},sg={class:"flex items-center justify-between mb-2"},ng={class:"text-sm text-purple-600 font-medium"},og={class:"text-sm"},ig={class:"font-bold text-purple-600 ml-2"},rg={class:"bg-gray-50 rounded-lg p-4"},lg={class:"space-y-2 text-sm"},ag={class:"flex justify-between"},cg={class:"font-medium"},ug={class:"flex justify-between"},dg={class:"font-medium"},fg={key:0,class:"flex justify-between"},pg={class:"font-medium"},mg={key:1,class:"flex justify-between"},hg={class:"font-medium"},gg={key:2,class:"flex justify-between"},xg={class:"font-medium"},yg=Ot({__name:"MaterialSummaryCard",setup(t){const e=ne(),s=$s();return(n,o)=>(F(),I("div",D0,[o[19]||(o[19]=ce('<div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-gray-900">Malzeme Özeti</h3><div class="flex items-center space-x-1"><svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg><span class="text-sm text-gray-500">Fire Payı Dahil</span></div></div>',1)),i("div",O0,[i("div",j0,[o[4]||(o[4]=ce('<div class="flex items-center justify-between mb-2"><div class="flex items-center space-x-2"><div class="w-4 h-4 bg-blue-500 rounded"></div><span class="font-medium text-gray-900">Hazır Beton</span></div><span class="text-sm text-blue-600 font-medium">C25/30</span></div>',1)),i("div",L0,[i("div",null,[o[0]||(o[0]=i("span",{class:"text-gray-600"},"Duvar:",-1)),i("span",F0,b(x(e).volumeCalculations.wallConcrete.toFixed(2))+" m³",1)]),i("div",null,[o[1]||(o[1]=i("span",{class:"text-gray-600"},"Temel:",-1)),i("span",I0,b(x(e).volumeCalculations.foundationConcrete.toFixed(2))+" m³",1)]),x(e).surfaceOptions.coping?(F(),I("div",B0,[o[2]||(o[2]=i("span",{class:"text-gray-600"},"Harpuşta:",-1)),i("span",N0,b(x(e).volumeCalculations.copingConcrete.toFixed(2))+" m³",1)])):ot("",!0),i("div",H0,[o[3]||(o[3]=i("span",{class:"text-gray-600"},"Toplam (Fire dahil):",-1)),i("span",z0,b((x(e).volumeCalculations.totalConcrete*(1+x(s).projectSettings.wastagePercentage/100)).toFixed(2))+" m³ ",1)])])]),i("div",W0,[o[8]||(o[8]=ce('<div class="flex items-center justify-between mb-2"><div class="flex items-center space-x-2"><div class="w-4 h-4 bg-orange-500 rounded"></div><span class="font-medium text-gray-900">İnşaat Demiri</span></div><span class="text-sm text-orange-600 font-medium">S420</span></div>',1)),i("div",V0,[i("div",null,[o[5]||(o[5]=i("span",{class:"text-gray-600"},"Temel:",-1)),i("span",K0,b((x(e).rebarCalculations.foundation.longitudinalWeight+x(e).rebarCalculations.foundation.stirrupWeight).toFixed(0))+" kg ",1)]),i("div",null,[o[6]||(o[6]=i("span",{class:"text-gray-600"},"Duvar:",-1)),i("span",U0,b((x(e).rebarCalculations.wall.verticalWeight+x(e).rebarCalculations.wall.horizontalWeight).toFixed(0))+" kg ",1)]),i("div",q0,[o[7]||(o[7]=i("span",{class:"text-gray-600"},"Toplam (Fire dahil):",-1)),i("span",G0,b((x(e).rebarCalculations.totalWeightTons*(1+x(s).projectSettings.wastagePercentage/100)).toFixed(2))+" ton ",1)])])]),x(e).surfaceOptions.plaster.enabled||x(e).surfaceOptions.paint.enabled?(F(),I("div",Y0,[x(e).surfaceOptions.plaster.enabled?(F(),I("div",J0,[i("div",Q0,[o[9]||(o[9]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-4 h-4 bg-green-500 rounded"}),i("span",{class:"font-medium text-gray-900"},"Sıva Harcı")],-1)),i("span",X0,b(x(e).surfaceOptions.plaster.doubleSided?"Çift Yüzey":"Tek Yüzey"),1)]),i("div",Z0,[o[10]||(o[10]=i("span",{class:"text-gray-600"},"Alan (Fire dahil):",-1)),i("span",tg,b((x(e).volumeCalculations.plasterArea*(1+x(s).projectSettings.wastagePercentage/100)).toFixed(2))+" m² ",1)])])):ot("",!0),x(e).surfaceOptions.paint.enabled?(F(),I("div",eg,[i("div",sg,[o[11]||(o[11]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-4 h-4 bg-purple-500 rounded"}),i("span",{class:"font-medium text-gray-900"},"Dış Cephe Boyası")],-1)),i("span",ng,b(x(e).surfaceOptions.paint.doubleSided?"Çift Yüzey":"Tek Yüzey"),1)]),i("div",og,[o[12]||(o[12]=i("span",{class:"text-gray-600"},"Alan (Fire dahil):",-1)),i("span",ig,b((x(e).volumeCalculations.paintArea*(1+x(s).projectSettings.wastagePercentage/100)).toFixed(2))+" m² ",1)])])):ot("",!0)])):ot("",!0),i("div",rg,[o[18]||(o[18]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Malzeme Maliyeti Dağılımı",-1)),i("div",lg,[i("div",ag,[o[13]||(o[13]=i("span",{class:"text-gray-600"},"Beton:",-1)),i("span",cg,"₺"+b(x(s).costBreakdown.materials.concrete.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",ug,[o[14]||(o[14]=i("span",{class:"text-gray-600"},"Demir:",-1)),i("span",dg,"₺"+b(x(s).costBreakdown.materials.rebar.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),x(e).surfaceOptions.plaster.enabled?(F(),I("div",fg,[o[15]||(o[15]=i("span",{class:"text-gray-600"},"Sıva:",-1)),i("span",pg,"₺"+b(x(s).costBreakdown.materials.plaster.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])):ot("",!0),x(e).surfaceOptions.paint.enabled?(F(),I("div",mg,[o[16]||(o[16]=i("span",{class:"text-gray-600"},"Boya:",-1)),i("span",hg,"₺"+b(x(s).costBreakdown.materials.paint.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])):ot("",!0),x(e).surfaceOptions.coping?(F(),I("div",gg,[o[17]||(o[17]=i("span",{class:"text-gray-600"},"Harpuşta:",-1)),i("span",xg,"₺"+b(x(s).costBreakdown.materials.coping.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])):ot("",!0)])])])]))}}),vg={class:"h-full flex flex-col space-y-4"},bg={class:"grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6"},_g={class:"lg:col-span-2"},wg={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-4 lg:p-6"},kg={class:"space-y-4 lg:space-y-6"},Sg={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-4 lg:p-6"},Cg={class:"space-y-4"},$g={class:"flex items-center justify-between"},Pg={class:"font-semibold text-gray-900"},Tg={class:"flex items-center justify-between"},Eg={class:"font-semibold text-gray-900"},Mg={class:"flex items-center justify-between"},Rg={class:"font-semibold text-gray-900"},Ag={class:"flex items-center justify-between"},Dg={class:"font-semibold text-gray-900"},Og=Ot({__name:"HomeView",setup(t){const e=ne();return(s,n)=>(F(),I("div",vg,[i("div",bg,[i("div",_g,[i("div",wg,[n[0]||(n[0]=ce('<div class="flex items-center justify-between mb-4"><h2 class="text-lg font-semibold text-gray-900">Duvar Kesiti</h2><div class="flex items-center space-x-2 text-sm text-gray-500"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><span>Gerçek zamanlı görselleştirme</span></div></div>',1)),it(s0)])]),i("div",kg,[it(A0),it(yg),i("div",Sg,[n[5]||(n[5]=i("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Hızlı İstatistikler",-1)),i("div",Cg,[i("div",$g,[n[1]||(n[1]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-blue-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"Toplam Beton")],-1)),i("span",Pg,b(x(e).volumeCalculations.totalConcrete.toFixed(2))+" m³ ",1)]),i("div",Tg,[n[2]||(n[2]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-orange-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"Toplam Demir")],-1)),i("span",Eg,b(x(e).rebarCalculations.totalWeightTons.toFixed(2))+" ton ",1)]),i("div",Mg,[n[3]||(n[3]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-green-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"Sıva Alanı")],-1)),i("span",Rg,b(x(e).volumeCalculations.plasterArea.toFixed(2))+" m² ",1)]),i("div",Ag,[n[4]||(n[4]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-purple-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"Boya Alanı")],-1)),i("span",Dg,b(x(e).volumeCalculations.paintArea.toFixed(2))+" m² ",1)])])])])]),n[6]||(n[6]=ce('<div class="bg-gradient-to-r from-primary-50 to-blue-50 rounded-xl border border-primary-200 p-6"><div class="flex items-start space-x-4"><div class="flex-shrink-0"><div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center"><svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div></div><div><h3 class="text-lg font-semibold text-gray-900 mb-2">Nasıl Kullanılır?</h3><div class="text-sm text-gray-700 space-y-1"><p>• <strong>Sol panel</strong>den duvar boyutlarını, donatı detaylarını ve yüzey işlemlerini ayarlayın</p><p>• <strong>Görselleştirme</strong> alanında duvarın kesitini gerçek zamanlı olarak görün</p><p>• <strong>Maliyet kartları</strong>nda anlık hesaplamaları takip edin</p><p>• <strong>Fiyatlar</strong> butonundan birim fiyatları özelleştirin</p><p>• <strong>Rapor</strong> butonundan detaylı analiz alın</p></div></div></div></div>',1))]))}}),jg=Ru({history:lu("/"),routes:[{path:"/",name:"home",component:Og},{path:"/about",name:"about",component:()=>Rh(()=>import("./AboutView-CH2aCko6.js"),[])}]}),ro=wc(Th);ro.use(Cc());ro.use(jg);ro.mount("#app");export{Nf as _,ce as a,I as c,F as o};
