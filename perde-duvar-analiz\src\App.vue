<script setup lang="ts">
import { RouterView } from 'vue-router'
import { useUIStore } from './stores/ui'
import AppHeader from './components/layout/AppHeader.vue'
import NotificationContainer from './components/ui/NotificationContainer.vue'
import PricingModal from './components/modals/PricingModal.vue'
import ReportModal from './components/modals/ReportModal.vue'

const uiStore = useUIStore()
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header with integrated tabs -->
    <AppHeader />

    <!-- Main Content -->
    <main class="pt-32">
      <div class="min-h-full p-4 lg:p-6">
        <RouterView />
      </div>
    </main>

    <!-- Modals -->
    <PricingModal />
    <ReportModal />

    <!-- Notifications -->
    <NotificationContainer />

    <!-- Loading Overlay -->
    <div
      v-if="uiStore.loading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span class="text-gray-700">Hesaplanıyor...</span>
      </div>
    </div>
  </div>
</template>