<script setup lang="ts">
import { useWallCalculationStore } from '@/stores/wallCalculation'

const wallStore = useWallCalculationStore()

const updateSurfaceOption = (key: string, value: boolean) => {
  wallStore.updateSurfaceOptions({ [key]: value })
}

const updatePlasterOption = (key: string, value: boolean) => {
  wallStore.updateSurfaceOptions({
    plaster: {
      ...wallStore.surfaceOptions.plaster,
      [key]: value
    }
  })
}

const updatePaintOption = (key: string, value: boolean) => {
  wallStore.updateSurfaceOptions({
    paint: {
      ...wallStore.surfaceOptions.paint,
      [key]: value
    }
  })
}
</script>

<template>
  <div class="space-y-6">
    <!-- Harpuşta -->
    <div>
      <h3 class="section-title">Harpuşta</h3>
      
      <div class="flex items-center">
        <input
          id="coping"
          type="checkbox"
          :checked="wallStore.surfaceOptions.coping"
          @change="updateSurfaceOption('coping', ($event.target as HTMLInputElement).checked)"
          class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
        />
        <label for="coping" class="ml-2 block text-sm text-gray-900">
          Duvar üstü harpuşta yapılsın
        </label>
      </div>
      
      <p class="mt-2 text-xs text-gray-500">
        Harpuşta, duvarın üst kısmına yapılan koruyucu beton katmanıdır (10cm yükseklik).
      </p>
    </div>

    <!-- Sıva -->
    <div>
      <h3 class="section-title">Sıva</h3>
      
      <div class="space-y-3">
        <div class="flex items-center">
          <input
            id="plaster"
            type="checkbox"
            :checked="wallStore.surfaceOptions.plaster.enabled"
            @change="updatePlasterOption('enabled', ($event.target as HTMLInputElement).checked)"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label for="plaster" class="ml-2 block text-sm text-gray-900">
            Sıva yapılsın
          </label>
        </div>

        <div v-if="wallStore.surfaceOptions.plaster.enabled" class="ml-6">
          <div class="flex items-center">
            <input
              id="plaster-double"
              type="checkbox"
              :checked="wallStore.surfaceOptions.plaster.doubleSided"
              @change="updatePlasterOption('doubleSided', ($event.target as HTMLInputElement).checked)"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="plaster-double" class="ml-2 block text-sm text-gray-900">
              Çift yüzey sıva
            </label>
          </div>
          <p class="mt-1 text-xs text-gray-500">
            İşaretlenirse duvarın her iki yüzeyine sıva yapılır.
          </p>
        </div>
      </div>
    </div>

    <!-- Boya -->
    <div>
      <h3 class="section-title">Boya</h3>
      
      <div class="space-y-3">
        <div class="flex items-center">
          <input
            id="paint"
            type="checkbox"
            :checked="wallStore.surfaceOptions.paint.enabled"
            @change="updatePaintOption('enabled', ($event.target as HTMLInputElement).checked)"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label for="paint" class="ml-2 block text-sm text-gray-900">
            Boya yapılsın
          </label>
        </div>

        <div v-if="wallStore.surfaceOptions.paint.enabled" class="ml-6">
          <div class="flex items-center">
            <input
              id="paint-double"
              type="checkbox"
              :checked="wallStore.surfaceOptions.paint.doubleSided"
              @change="updatePaintOption('doubleSided', ($event.target as HTMLInputElement).checked)"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="paint-double" class="ml-2 block text-sm text-gray-900">
              Çift yüzey boya
            </label>
          </div>
          <p class="mt-1 text-xs text-gray-500">
            İşaretlenirse duvarın her iki yüzeyine boya yapılır.
          </p>
        </div>
      </div>
    </div>

    <!-- Yüzey Özeti -->
    <div class="bg-gray-50 rounded-lg p-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">Yüzey İşlemleri Özeti</h4>
      <div class="space-y-2 text-sm">
        <div class="flex justify-between">
          <span class="text-gray-500">Harpuşta:</span>
          <span class="font-medium">{{ wallStore.surfaceOptions.coping ? 'Evet' : 'Hayır' }}</span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-gray-500">Sıva Alanı:</span>
          <span class="font-medium">
            {{ wallStore.volumeCalculations.plasterArea.toFixed(2) }} m²
            {{ wallStore.surfaceOptions.plaster.doubleSided ? '(Çift Yüzey)' : '(Tek Yüzey)' }}
          </span>
        </div>
        
        <div class="flex justify-between">
          <span class="text-gray-500">Boya Alanı:</span>
          <span class="font-medium">
            {{ wallStore.volumeCalculations.paintArea.toFixed(2) }} m²
            {{ wallStore.surfaceOptions.paint.doubleSided ? '(Çift Yüzey)' : '(Tek Yüzey)' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
