import{_ as l,c as e,a as i,o as t}from"./index-DKXikqKA.js";const s={},r={class:"max-w-4xl mx-auto"};function o(n,a){return t(),e("div",r,a[0]||(a[0]=[i('<div class="bg-white rounded-xl shadow-lg p-8"><h1 class="text-3xl font-bold text-gray-900 mb-6">Per<PERSON></h1><div class="prose prose-lg max-w-none"><p class="text-gray-600 mb-4"> <PERSON><PERSON> uy<PERSON>, perde beton duvarların profesyonel analizi ve maliyet hesaplaması için geliştirilmiş kapsamlı bir araçtır. </p><h2 class="text-xl font-semibold text-gray-900 mt-6 mb-3">Özellikler</h2><ul class="list-disc list-inside text-gray-600 space-y-2"><li>Detaylı boyut hesaplamaları</li><li>Donatı analizi ve optimizasyonu</li><li>Yüzey işlemleri planlaması</li><li>Kapsamlı maliyet analizi</li><li>Profesyonel raporlama</li></ul><h2 class="text-xl font-semibold text-gray-900 mt-6 mb-3">Teknik Bilgiler</h2><p class="text-gray-600"> Vue.js 3, TypeScript ve Tailwind CSS kullanılarak geliştirilmiştir. Tüm hesaplamalar Türk yapı standartlarına uygun olarak yapılmaktadır. </p></div></div>',1)]))}const p=l(s,[["render",o]]);export{p as default};
