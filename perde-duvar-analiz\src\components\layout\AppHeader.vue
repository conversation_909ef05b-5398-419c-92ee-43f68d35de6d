<script setup lang="ts">
import { ref } from 'vue'
import { useUIStore } from '@/stores/ui'
import { usePricingStore } from '@/stores/pricing'
import { useWallCalculationStore } from '@/stores/wallCalculation'
import DimensionsPanel from '@/components/panels/DimensionsPanel.vue'
import RebarPanel from '@/components/panels/RebarPanel.vue'
import SurfacePanel from '@/components/panels/SurfacePanel.vue'

const uiStore = useUIStore()
const pricingStore = usePricingStore()
const wallStore = useWallCalculationStore()

const mobileMenuOpen = ref(false)

const tabs = [
  {
    id: 'dimensions' as const,
    name: '<PERSON><PERSON><PERSON>',
    icon: 'M4 6h16M4 12h16M4 18h16'
  },
  {
    id: 'rebar' as const,
    name: 'Donat<PERSON>',
    icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
  },
  {
    id: 'surface' as const,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    icon: 'M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z'
  }
]

const setActiveTab = (tabId: typeof uiStore.activeTab) => {
  uiStore.setActiveTab(tabId)
}

const openPricingModal = () => {
  uiStore.openPricingModal()
}

const openReportModal = () => {
  uiStore.openReportModal()
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}
</script>

<template>
  <header class="bg-white shadow-lg border-b border-gray-200 fixed top-0 left-0 right-0 z-40">
    <!-- Main Header -->
    <div class="px-4 lg:px-6 py-4">
      <div class="flex items-center justify-between">
        <!-- Left Section - Logo and Title -->
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-blue-600 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <div>
            <h1 class="text-xl font-bold text-gray-900">Perde Duvar Analiz</h1>
            <p class="text-sm text-gray-500">Profesyonel Hesaplama Aracı</p>
          </div>
        </div>

        <!-- Center Section - Quick Stats -->
        <div class="hidden lg:flex items-center space-x-6">
          <div class="text-center">
            <div class="text-lg font-semibold text-gray-900">
              {{ wallStore.dimensions.length }}m
            </div>
            <div class="text-xs text-gray-500">Uzunluk</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-gray-900">
              {{ wallStore.dimensions.height }}m
            </div>
            <div class="text-xs text-gray-500">Yükseklik</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-primary-600">
              {{ wallStore.volumeCalculations.totalConcrete.toFixed(2) }}m³
            </div>
            <div class="text-xs text-gray-500">Toplam Beton</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-primary-600">
              ₺{{ pricingStore.finalPricing.finalTotal.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}
            </div>
            <div class="text-xs text-gray-500">Toplam Maliyet</div>
          </div>
        </div>

        <!-- Right Section - Navigation and Actions -->
        <div class="flex items-center space-x-3">
          <!-- Navigation Links -->
          <nav class="hidden md:flex items-center space-x-4">
            <router-link
              to="/"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              active-class="text-primary-600 bg-primary-50"
            >
              Ana Sayfa
            </router-link>
            <router-link
              to="/about"
              class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              active-class="text-primary-600 bg-primary-50"
            >
              Hakkında
            </router-link>
          </nav>

          <!-- Action Buttons -->
          <div class="flex items-center space-x-2">
            <!-- Pricing Button -->
            <button
              @click="openPricingModal"
              class="flex items-center space-x-2 px-3 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors duration-200 text-sm"
              title="Fiyat Ayarları"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
              <span class="hidden lg:inline">Fiyatlar</span>
            </button>

            <!-- Report Button -->
            <button
              @click="openReportModal"
              class="flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 text-sm"
              title="Rapor Oluştur"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span class="hidden lg:inline">Rapor</span>
            </button>

            <!-- Mobile Menu Button -->
            <button
              @click="toggleMobileMenu"
              class="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
              :aria-expanded="mobileMenuOpen"
              aria-label="Ana menüyü aç"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path v-if="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile Navigation Menu -->
      <div v-if="mobileMenuOpen" class="md:hidden border-t border-gray-200 bg-white">
        <div class="px-4 py-3 space-y-2">
          <router-link
            to="/"
            @click="mobileMenuOpen = false"
            class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors"
            active-class="text-primary-600 bg-primary-50"
          >
            Ana Sayfa
          </router-link>
          <router-link
            to="/about"
            @click="mobileMenuOpen = false"
            class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors"
            active-class="text-primary-600 bg-primary-50"
          >
            Hakkında
          </router-link>
        </div>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="bg-gray-50 border-t border-gray-200">
      <div class="px-4 lg:px-6">
        <nav class="flex space-x-0" aria-label="Hesaplama Seçenekleri" role="tablist">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="setActiveTab(tab.id)"
            :class="[
              'flex-1 lg:flex-none lg:px-6 py-3 px-2 text-center text-sm font-medium transition-colors duration-200 border-b-2',
              uiStore.activeTab === tab.id
                ? 'border-primary-500 text-primary-600 bg-white'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-100'
            ]"
            :aria-selected="uiStore.activeTab === tab.id"
            :aria-controls="`panel-${tab.id}`"
            role="tab"
            :tabindex="uiStore.activeTab === tab.id ? 0 : -1"
          >
            <div class="flex flex-col lg:flex-row items-center lg:space-x-2 space-y-1 lg:space-y-0">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="tab.icon" />
              </svg>
              <span class="text-xs lg:text-sm">{{ tab.name }}</span>
            </div>
          </button>
        </nav>
      </div>
    </div>

    <!-- Tab Content Panel -->
    <div class="bg-white border-t border-gray-200 shadow-lg">
      <div class="px-4 lg:px-6 py-4 max-h-80 overflow-y-auto">
        <!-- Dimensions Panel -->
        <div
          v-if="uiStore.activeTab === 'dimensions'"
          :id="`panel-dimensions`"
          role="tabpanel"
          aria-labelledby="tab-dimensions"
        >
          <DimensionsPanel />
        </div>

        <!-- Rebar Panel -->
        <div
          v-if="uiStore.activeTab === 'rebar'"
          :id="`panel-rebar`"
          role="tabpanel"
          aria-labelledby="tab-rebar"
        >
          <RebarPanel />
        </div>

        <!-- Surface Panel -->
        <div
          v-if="uiStore.activeTab === 'surface'"
          :id="`panel-surface`"
          role="tabpanel"
          aria-labelledby="tab-surface"
        >
          <SurfacePanel />
        </div>
      </div>
    </div>
  </header>
</template>
