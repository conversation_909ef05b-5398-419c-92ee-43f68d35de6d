import { test, expect } from '@playwright/test';

test('visits the app root url', async ({ page }) => {
  await page.goto('/');
  await expect(page.locator('h1')).toHaveText('Perde Duvar <PERSON>liz');
});

test('can navigate to about page', async ({ page }) => {
  await page.goto('/');
  await page.click('text=Hakkında');
  await expect(page.locator('h1')).toHaveText('Perde Duvar Analiz Hakkında');
});

test('can interact with dimension inputs', async ({ page }) => {
  await page.goto('/');

  // Check if dimension inputs are visible
  await expect(page.locator('input[type="number"]').first()).toBeVisible();

  // Test input interaction
  const lengthInput = page.locator('input').first();
  await lengthInput.fill('15');
  await expect(lengthInput).toHaveValue('15');
});
