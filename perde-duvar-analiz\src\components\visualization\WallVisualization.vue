<script setup lang="ts">
import { computed } from 'vue'
import { useWallCalculationStore } from '@/stores/wallCalculation'

const wallStore = useWallCalculationStore()

// SVG boyutları ve ölçekleme
const SVG_WIDTH = 800
const SVG_HEIGHT = 500
const MARGIN = 50

// Computed properties for scaled dimensions
const scaledDimensions = computed(() => {
  const { length, height, thickness, foundationDepth, foundationWidth } = wallStore.dimensions
  
  // Maksimum boyutları belirle
  const maxLength = Math.max(length, foundationWidth / 100)
  const maxHeight = height + (foundationDepth / 100)
  
  // Ölçekleme faktörü
  const scaleX = (SVG_WIDTH - 2 * MARGIN) / maxLength
  const scaleY = (SVG_HEIGHT - 2 * MARGIN) / maxHeight
  const scale = Math.min(scaleX, scaleY)
  
  return {
    scale,
    wallHeight: height * scale,
    wallThickness: (thickness / 100) * scale,
    foundationDepth: (foundationDepth / 100) * scale,
    foundationWidth: (foundationWidth / 100) * scale,
    centerX: SVG_WIDTH / 2,
    centerY: SVG_HEIGHT / 2
  }
})

// Wall and foundation coordinates
const wallCoords = computed(() => {
  const { wallHeight, wallThickness, foundationDepth, foundationWidth, centerX, centerY } = scaledDimensions.value
  
  // Foundation coordinates (bottom)
  const foundationX = centerX - foundationWidth / 2
  const foundationY = centerY + wallHeight / 2
  
  // Wall coordinates (on top of foundation)
  const wallX = centerX - wallThickness / 2
  const wallY = foundationY - wallHeight
  
  return {
    foundation: {
      x: foundationX,
      y: foundationY,
      width: foundationWidth,
      height: foundationDepth
    },
    wall: {
      x: wallX,
      y: wallY,
      width: wallThickness,
      height: wallHeight
    }
  }
})

// Rebar visualization
const rebarLines = computed(() => {
  const { wall } = wallCoords.value
  const { verticalSpacing, horizontalSpacing } = wallStore.rebarDetails.wall
  const { concreteCover } = wallStore.rebarDetails.general
  const { scale } = scaledDimensions.value
  
  const coverScaled = (concreteCover / 100) * scale
  const lines: Array<{ x1: number, y1: number, x2: number, y2: number, type: 'vertical' | 'horizontal' }> = []
  
  // Vertical rebars in wall
  const verticalSpacingScaled = (verticalSpacing / 100) * scale
  const verticalCount = Math.floor(wall.width / verticalSpacingScaled) + 1
  
  for (let i = 0; i < verticalCount; i++) {
    const x = wall.x + coverScaled + (i * verticalSpacingScaled)
    if (x <= wall.x + wall.width - coverScaled) {
      lines.push({
        x1: x,
        y1: wall.y + coverScaled,
        x2: x,
        y2: wall.y + wall.height - coverScaled,
        type: 'vertical'
      })
    }
  }
  
  // Horizontal rebars in wall
  const horizontalSpacingScaled = (horizontalSpacing / 100) * scale
  const horizontalCount = Math.floor(wall.height / horizontalSpacingScaled) + 1
  
  for (let i = 0; i < horizontalCount; i++) {
    const y = wall.y + coverScaled + (i * horizontalSpacingScaled)
    if (y <= wall.y + wall.height - coverScaled) {
      lines.push({
        x1: wall.x + coverScaled,
        y1: y,
        x2: wall.x + wall.width - coverScaled,
        y2: y,
        type: 'horizontal'
      })
    }
  }
  
  return lines
})

// Dimension labels with lines
const dimensionLabels = computed(() => {
  const { wall, foundation } = wallCoords.value
  const { length, height, thickness, foundationDepth, foundationWidth } = wallStore.dimensions

  return [
    // Wall length (top)
    {
      x: wall.x + wall.width / 2,
      y: wall.y - 30,
      text: `${length}m`,
      type: 'length',
      line: {
        x1: wall.x,
        y1: wall.y - 15,
        x2: wall.x + wall.width,
        y2: wall.y - 15
      }
    },
    // Wall height (left)
    {
      x: wall.x - 40,
      y: wall.y + wall.height / 2,
      text: `${height}m`,
      type: 'height',
      line: {
        x1: wall.x - 25,
        y1: wall.y,
        x2: wall.x - 25,
        y2: wall.y + wall.height
      }
    },
    // Wall thickness (right)
    {
      x: wall.x + wall.width + 30,
      y: wall.y + wall.height / 2,
      text: `${thickness}cm`,
      type: 'thickness',
      line: {
        x1: wall.x + wall.width + 15,
        y1: wall.y,
        x2: wall.x + wall.width + 15,
        y2: wall.y + wall.height
      }
    },
    // Foundation depth (left)
    {
      x: foundation.x - 40,
      y: foundation.y + foundation.height / 2,
      text: `${foundationDepth}cm`,
      type: 'foundation',
      line: {
        x1: foundation.x - 25,
        y1: foundation.y,
        x2: foundation.x - 25,
        y2: foundation.y + foundation.height
      }
    },
    // Foundation width (bottom)
    {
      x: foundation.x + foundation.width / 2,
      y: foundation.y + foundation.height + 30,
      text: `${foundationWidth}cm`,
      type: 'foundation',
      line: {
        x1: foundation.x,
        y1: foundation.y + foundation.height + 15,
        x2: foundation.x + foundation.width,
        y2: foundation.y + foundation.height + 15
      }
    }
  ]
})

// Ground line
const groundLine = computed(() => {
  const { foundation } = wallCoords.value
  return {
    x1: foundation.x - 50,
    y1: foundation.y + foundation.height,
    x2: foundation.x + foundation.width + 50,
    y2: foundation.y + foundation.height
  }
})
</script>

<template>
  <div class="w-full">
    <svg
      :width="SVG_WIDTH"
      :height="SVG_HEIGHT"
      class="w-full h-auto border border-gray-200 rounded-lg bg-gray-50"
      viewBox="0 0 800 500"
      preserveAspectRatio="xMidYMid meet"
    >
      <!-- Definitions -->
      <defs>
        <!-- Grid pattern -->
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" stroke-width="0.5"/>
        </pattern>

        <!-- Arrow marker -->
        <marker id="arrowhead" markerWidth="10" markerHeight="7"
                refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6" />
        </marker>

        <!-- Concrete pattern -->
        <pattern id="concrete" patternUnits="userSpaceOnUse" width="4" height="4">
          <rect width="4" height="4" fill="#f3f4f6"/>
          <circle cx="2" cy="2" r="0.5" fill="#d1d5db"/>
        </pattern>

        <!-- Foundation pattern -->
        <pattern id="foundation" patternUnits="userSpaceOnUse" width="6" height="6">
          <rect width="6" height="6" fill="#d1d5db"/>
          <circle cx="3" cy="3" r="0.8" fill="#9ca3af"/>
        </pattern>

        <!-- Plaster gradient -->
        <linearGradient id="plasterGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8" />
          <stop offset="100%" style="stop-color:#10b981;stop-opacity:0.4" />
        </linearGradient>
      </defs>

      <!-- Background -->
      <rect width="100%" height="100%" fill="url(#grid)" />
      
      <!-- Foundation -->
      <rect
        :x="wallCoords.foundation.x"
        :y="wallCoords.foundation.y"
        :width="wallCoords.foundation.width"
        :height="wallCoords.foundation.height"
        fill="url(#foundation)"
        stroke="#9ca3af"
        stroke-width="2"
        rx="3"
      />

      <!-- Foundation border highlight -->
      <rect
        :x="wallCoords.foundation.x"
        :y="wallCoords.foundation.y"
        :width="wallCoords.foundation.width"
        :height="wallCoords.foundation.height"
        fill="none"
        stroke="#6b7280"
        stroke-width="1"
        rx="3"
        opacity="0.5"
      />

      <!-- Foundation label -->
      <text
        :x="wallCoords.foundation.x + wallCoords.foundation.width / 2"
        :y="wallCoords.foundation.y + wallCoords.foundation.height / 2"
        text-anchor="middle"
        dominant-baseline="middle"
        class="text-sm font-bold fill-gray-700"
        style="text-shadow: 1px 1px 2px rgba(255,255,255,0.8)"
      >
        TEMEL
      </text>

      <!-- Wall -->
      <rect
        :x="wallCoords.wall.x"
        :y="wallCoords.wall.y"
        :width="wallCoords.wall.width"
        :height="wallCoords.wall.height"
        fill="url(#concrete)"
        stroke="#6b7280"
        stroke-width="2"
        rx="3"
      />

      <!-- Wall border highlight -->
      <rect
        :x="wallCoords.wall.x"
        :y="wallCoords.wall.y"
        :width="wallCoords.wall.width"
        :height="wallCoords.wall.height"
        fill="none"
        stroke="#4b5563"
        stroke-width="1"
        rx="3"
        opacity="0.7"
      />

      <!-- Wall label -->
      <text
        :x="wallCoords.wall.x + wallCoords.wall.width / 2"
        :y="wallCoords.wall.y + wallCoords.wall.height / 2"
        text-anchor="middle"
        dominant-baseline="middle"
        class="text-sm font-bold fill-gray-800"
        style="text-shadow: 1px 1px 2px rgba(255,255,255,0.8)"
      >
        PERDE DUVAR
      </text>
      
      <!-- Rebar lines -->
      <g v-if="rebarLines.length > 0">
        <line
          v-for="(line, index) in rebarLines"
          :key="index"
          :x1="line.x1"
          :y1="line.y1"
          :x2="line.x2"
          :y2="line.y2"
          :stroke="line.type === 'vertical' ? '#dc2626' : '#ea580c'"
          stroke-width="1.5"
          opacity="0.8"
        />
      </g>
      
      <!-- Coping (if enabled) -->
      <rect
        v-if="wallStore.surfaceOptions.coping"
        :x="wallCoords.wall.x - 5"
        :y="wallCoords.wall.y - 15"
        :width="wallCoords.wall.width + 10"
        height="15"
        fill="#fbbf24"
        stroke="#f59e0b"
        stroke-width="1"
        rx="2"
      />
      
      <!-- Plaster layers (if enabled) -->
      <g v-if="wallStore.surfaceOptions.plaster.enabled">
        <!-- Left plaster -->
        <rect
          :x="wallCoords.wall.x - 4"
          :y="wallCoords.wall.y - 2"
          width="4"
          :height="wallCoords.wall.height + 4"
          fill="url(#plasterGradient)"
          stroke="#059669"
          stroke-width="0.5"
          rx="2"
        />
        <!-- Right plaster (if double-sided) -->
        <rect
          v-if="wallStore.surfaceOptions.plaster.doubleSided"
          :x="wallCoords.wall.x + wallCoords.wall.width"
          :y="wallCoords.wall.y - 2"
          width="4"
          :height="wallCoords.wall.height + 4"
          fill="url(#plasterGradient)"
          stroke="#059669"
          stroke-width="0.5"
          rx="2"
        />
      </g>
      
      <!-- Ground line -->
      <line
        :x1="groundLine.x1"
        :y1="groundLine.y1"
        :x2="groundLine.x2"
        :y2="groundLine.y2"
        stroke="#8b5cf6"
        stroke-width="3"
        stroke-dasharray="5,5"
      />

      <!-- Ground pattern -->
      <g>
        <circle v-for="i in 8" :key="i"
          :cx="groundLine.x1 + (i * 20)"
          :cy="groundLine.y1 + 10"
          r="2"
          fill="#8b5cf6"
          opacity="0.6"
        />
      </g>

      <!-- Dimension lines and labels -->
      <g>
        <!-- Dimension lines -->
        <line
          v-for="(label, index) in dimensionLabels"
          :key="`line-${index}`"
          :x1="label.line.x1"
          :y1="label.line.y1"
          :x2="label.line.x2"
          :y2="label.line.y2"
          stroke="#3b82f6"
          stroke-width="2"
          marker-start="url(#arrowhead)"
          marker-end="url(#arrowhead)"
        />

        <!-- Dimension labels -->
        <text
          v-for="(label, index) in dimensionLabels"
          :key="`text-${index}`"
          :x="label.x"
          :y="label.y"
          text-anchor="middle"
          dominant-baseline="middle"
          class="text-xs font-bold fill-primary-600"
        >
          {{ label.text }}
        </text>

        <!-- Label backgrounds -->
        <rect
          v-for="(label, index) in dimensionLabels"
          :key="`bg-${index}`"
          :x="label.x - 15"
          :y="label.y - 8"
          width="30"
          height="16"
          fill="white"
          stroke="#3b82f6"
          stroke-width="1"
          rx="3"
          opacity="0.9"
        />

        <!-- Re-render text on top -->
        <text
          v-for="(label, index) in dimensionLabels"
          :key="`text-top-${index}`"
          :x="label.x"
          :y="label.y"
          text-anchor="middle"
          dominant-baseline="middle"
          class="text-xs font-bold fill-primary-600"
        >
          {{ label.text }}
        </text>
      </g>
      
      <!-- Legend -->
      <g transform="translate(20, 20)">
        <rect x="0" y="0" width="150" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="4" opacity="0.95"/>
        <text x="10" y="15" class="text-xs font-semibold fill-gray-900">Açıklama</text>
        
        <!-- Foundation -->
        <rect x="10" y="25" width="12" height="8" fill="#d1d5db" stroke="#9ca3af"/>
        <text x="28" y="32" class="text-xs fill-gray-700">Temel</text>
        
        <!-- Wall -->
        <rect x="10" y="40" width="12" height="8" fill="#f3f4f6" stroke="#6b7280"/>
        <text x="28" y="47" class="text-xs fill-gray-700">Duvar</text>
        
        <!-- Vertical rebar -->
        <line x1="10" y1="55" x2="22" y2="55" stroke="#dc2626" stroke-width="2"/>
        <text x="28" y="58" class="text-xs fill-gray-700">Dikey Donatı</text>
        
        <!-- Horizontal rebar -->
        <line x1="10" y1="70" x2="22" y2="70" stroke="#ea580c" stroke-width="2"/>
        <text x="28" y="73" class="text-xs fill-gray-700">Yatay Donatı</text>
        
        <!-- Plaster -->
        <rect v-if="wallStore.surfaceOptions.plaster.enabled" x="10" y="85" width="12" height="8" fill="#10b981" opacity="0.6"/>
        <text v-if="wallStore.surfaceOptions.plaster.enabled" x="28" y="92" class="text-xs fill-gray-700">Sıva</text>
        
        <!-- Coping -->
        <rect v-if="wallStore.surfaceOptions.coping" x="10" y="100" width="12" height="8" fill="#fbbf24"/>
        <text v-if="wallStore.surfaceOptions.coping" x="28" y="107" class="text-xs fill-gray-700">Harpuşta</text>
      </g>
    </svg>
  </div>
</template>
